import { Block, Entity, system, world } from "@minecraft/server";
import { handleCameraShakeScriptEvent, cameraShake } from "./bosses/general_effects/camerashake";
import { initializeItemCustomComponents } from "./items/index";
import { checkBossSummoningRequirements, hasRelevantBaseItems } from "./bosses/general_mechanics/bossSummoner";
import { grimhowlMechanics } from "./bosses/grimhowl/index";
import { shockwave } from "./bosses/general_attacks/shockwave";
import { handleArmorSets } from "./player/armors/sets";
import { cleanupPlayerState } from "./player/armors/piglinChampion";
import "./entities/index"; // Import entities module for initialization
import "./player/index"; // Import player module for initialization
// Dimension references - initialized after world loads to avoid privilege errors
export let dimensions;
// Initialize custom components
system.beforeEvents.startup.subscribe((data) => {
    const itemComponentRegistry = data.itemComponentRegistry;
    initializeItemCustomComponents(itemComponentRegistry);
});
/**
 * Initialize dimensions after world is loaded to avoid privilege errors
 */
function initializeDimensions() {
    try {
        dimensions = new Set(["overworld", "nether", "the_end"].map((dimension) => world.getDimension(dimension)));
        console.log("[Better Bosses] Successfully initialized dimensions");
    }
    catch (error) {
        console.error("[Better Bosses] Failed to initialize dimensions:", error);
        // Fallback: try again after a short delay
        system.runTimeout(() => {
            try {
                dimensions = new Set(["overworld", "nether", "the_end"].map((dimension) => world.getDimension(dimension)));
                console.log("[Better Bosses] Successfully initialized dimensions on retry");
            }
            catch (retryError) {
                console.error("[Better Bosses] Failed to initialize dimensions on retry:", retryError);
            }
        }, 20);
    }
}
/**
 * Main game loop initialization
 * Sets up event listeners and periodic updates for boss mechanics
 */
system.runTimeout(() => {
    // Initialize dimensions now that world is loaded
    initializeDimensions();
    // Schedule a timeout to start the interval
    // MAIN TICK
    system.runInterval(() => {
        // Ensure dimensions are initialized before using them
        if (!dimensions) {
            return;
        }
        dimensions.forEach((dimension) => {
            // Iterate over each dimension
            // Only check for boss summoning requirements if there are relevant base items present
            if (hasRelevantBaseItems(dimension)) {
                checkBossSummoningRequirements(dimension);
            }
        });
        // Handle armor sets for all players
        const allPlayers = world.getAllPlayers();
        allPlayers.forEach((player) => {
            try {
                handleArmorSets(player);
            }
            catch (error) {
                // Silently handle errors to prevent tick interruption
                console.warn(`[Better Bosses] Error handling armor sets for player ${player.name}:`, error);
            }
        });
    });
    // Listen for script events
    system.afterEvents.scriptEventReceive.subscribe((data) => {
        const entity = data.sourceEntity;
        const block = data.sourceBlock;
        const eventId = data.id;
        if (entity)
            scriptEventDispatcher(entity, eventId);
        if (block)
            scriptEventDispatcher(block, eventId);
        return;
    });
    // Handle player disconnections for armor set cleanup
    world.afterEvents.playerLeave.subscribe((event) => {
        try {
            cleanupPlayerState(event.playerId);
        }
        catch (error) {
            console.warn(`[Better Bosses] Error cleaning up player state for ${event.playerId}:`, error);
        }
    });
    function scriptEventDispatcher(source, eventId) {
        if (source instanceof Entity) {
            const entityTypeId = source.typeId;
            switch (eventId) {
                // Handle camera shake events
                case "ptd_bb:camerashake":
                    handleCameraShakeScriptEvent(source);
                    break;
                // Handle horizontal attack events
                case "ptd_bb:horizontal_attack_end":
                    if (source.typeId === "ptd_bb:piglin_champion") {
                        source.triggerEvent("ptd_bb:reset_attack");
                    }
                    break;
                // Handle vertical attack events
                case "ptd_bb:vertical_attack_end":
                    if (source.typeId === "ptd_bb:piglin_champion") {
                        source.triggerEvent("ptd_bb:reset_attack");
                    }
                    break;
                // Handle piglin champion spawning effects
                case "ptd_bb:piglin_champion_spawn_shockwave":
                    if (source.typeId === "ptd_bb:piglin_champion") {
                        // Apply shockwave with radius 8, power 1.5, damage 8, excluding piglin_champion family
                        shockwave(source, 5, 1.5, 8, ["piglin_champion"]);
                        // Apply camera shake effect
                        cameraShake(source, 32, 0.02, 0.5, 0.5);
                        // Play a particle effect at the piglin's location
                        source.dimension.spawnParticle("minecraft:large_explosion", source.location);
                        // Play a sound effect
                        source.dimension.playSound("random.explode", source.location);
                    }
                    break;
                case "ptd_bb:piglin_champion_spawn_particle":
                    if (source.typeId === "ptd_bb:piglin_champion") {
                        // Play the final particle effect at the piglin's location
                        source.dimension.spawnParticle("ptd_bb:pg_spawn3_01", source.location);
                    }
                    break;
                default:
                    break;
            }
            if (entityTypeId === "ptd_bb:grimhowl") {
                grimhowlMechanics({ id: eventId, sourceEntity: source }, "script");
            }
        }
        else if (source instanceof Block) {
            // Handle block-specific script events
            return; // Just return for now since we don't have any custom blocks that fire scriptevents
        }
        return;
    }
}, 60); // Initial delay of 60 ticks before starting the interval

import {
  Dimension,
  EntityComponentTypes,
  EntityDamageCause,
  EntityEquippableComponent,
  EquipmentSlot,
  GameMode,
  ItemComponentTypes,
  ItemStack,
  Player,
  Vector3,
  system
} from "@minecraft/server";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../bosses/general_attacks/attackDamages";
import { NON_SOLID_BLOCKS } from "../../utilities/constants/nonSolidBlocks";
import { damageItemstack } from "../damageItemstack";

// Global map to track attack timers for players
const playerAttackTimers = new Map<string, number>();
// Global map to track interval IDs for players
const playerIntervalIds = new Map<string, number>();

export function piglinChampionAxeOnUse(player: Player, item: ItemStack) {
  try {
    // Apply a 15-second cooldown (300 ticks) to the axe (defined in the item json)
    const cooldownComponent = item.getComponent(ItemComponentTypes.Cooldown);
    if (cooldownComponent) {
      cooldownComponent.startCooldown(player);
    }

    // Check if there's already an attack in progress for this player
    if (!playerAttackTimers.has(player.id)) {
      // Start a new attack sequence
      startVerticalAttack(player);
    }
  } catch (error) {
    console.warn(`Error in piglinChampionAxeOnUse: ${error}`);
  }

  return;
}

/**
 * Starts the vertical attack sequence for a player
 * @param player The player who used the axe
 */
async function startVerticalAttack(player: Player): Promise<void> {
  // Initialize attack timer
  playerAttackTimers.set(player.id, 0);

  // Start interval to increment attack timer
  const intervalId = system.runInterval(() => {
    try {
      // Check if player still exists
      try {
        // Access a property to check if player is valid
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        player.id; // Just accessing the property to check if player is valid
      } catch (e) {
        cleanupAttack(player.id, intervalId);
        return;
      }

      // Check if player is still holding the axe
      const equippableComponent = player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent;
      if (!equippableComponent) {
        cleanupAttack(player.id, intervalId);
        return;
      }

      const mainHandItem = equippableComponent.getEquipment(EquipmentSlot.Mainhand);
      if (!mainHandItem || mainHandItem.type.id !== "ptd_bb:piglin_champion_axe") {
        cleanupAttack(player.id, intervalId);
        return;
      }

      // Get current attack timer
      const attackTimer = playerAttackTimers.get(player.id) || 0;

      // Increment attack timer
      playerAttackTimers.set(player.id, attackTimer + 1);

      // Execute attack at tick 19
      if (attackTimer === 19) {
        executePlayerVerticalAttack(player);
      }

      // End attack after 106 ticks (same as the Piglin Champion's vertical attack duration)
      if (attackTimer >= 106) {
        cleanupAttack(player.id, intervalId);
      }
    } catch (error) {
      console.warn(`Error in attack interval: ${error}`);
      cleanupAttack(player.id, intervalId);
    }
  });

  // Store interval ID
  playerIntervalIds.set(player.id, intervalId);
}

/**
 * Cleans up the attack resources for a player
 * @param playerId The ID of the player
 * @param intervalId The ID of the interval to clear
 */
function cleanupAttack(playerId: string, intervalId: number): void {
  // Clear interval
  system.clearRun(intervalId);

  // Remove player from maps
  playerAttackTimers.delete(playerId);
  playerIntervalIds.delete(playerId);
}

/**
 * Finds a position just above the ground (solid block) at the given x,z coordinates
 * @param dimension The dimension to check in
 * @param location The base location to check
 * @param maxSearchDistance Maximum vertical distance to search
 * @returns A location 0.001 blocks above a solid block, or undefined if none found
 */
function findGroundPosition(dimension: Dimension, location: Vector3, maxSearchDistance: number = 20): Vector3 | undefined {
  // Start from a higher position to ensure we find the ground in most cases
  const startY = location.y + 10;

  // Search downward for a solid block
  for (let y = 0; y <= maxSearchDistance * 2; y++) {
    const checkPos: Vector3 = { x: location.x, y: startY - y, z: location.z };

    // Get the block at this position
    const block = dimension.getBlock(checkPos);

    // If we found a non-air block
    if (block && !block.isAir) {
      // Check if it's a non-solid block (grass, flower, etc.)
      if (NON_SOLID_BLOCKS.has(block.type.id)) {
        // Instead of returning, continue searching downward
        continue;
      }

      // If it's a solid block, check if the block above is air or non-solid
      const blockAbovePos: Vector3 = { x: location.x, y: startY - y + 1, z: location.z };

      // Only return this position if the block above is air or a non-solid block
      if (isAirOrNonSolid(dimension, blockAbovePos)) {
        return {
          x: location.x,
          y: startY - y + 1.001, // Position slightly above the solid block
          z: location.z
        };
      }

      // If the block above isn't air or non-solid, continue searching downward
    }
  }

  // If we couldn't find a suitable ground position, return the original location
  return location;
}

/**
 * Checks if a block at the given location is an air block or a non-solid block
 * If it's a non-solid block, breaks it to simulate physics with particles
 * @param dimension The dimension to check in
 * @param location The location to check
 * @returns True if the block is an air block or a non-solid block, false otherwise
 */
function isAirOrNonSolid(dimension: Dimension, location: Vector3): boolean {
  const block = dimension.getBlock(location);
  if (!block) return true; // Treat as air if block is null
  if (block.isAir) return true;

  // If it's a non-solid block, break it to simulate physics
  if (NON_SOLID_BLOCKS.has(block.type.id)) {
    try {
      // Break the block using the setblock command with destroy flag for particles
      const x = Math.floor(location.x);
      const y = Math.floor(location.y);
      const z = Math.floor(location.z);
      dimension.runCommand(`setblock ${x} ${y} ${z} air [] destroy`);
    } catch (error) {
      console.warn(`Error breaking non-solid block with command: ${error}`);

      // Fallback to using setType if the command fails
      try {
        block.setType("minecraft:air");
      } catch (fallbackError) {
        console.warn(`Fallback error breaking non-solid block: ${fallbackError}`);
      }
    }
    return true;
  }

  return false;
}

/**
 * Executes the vertical attack for a player
 * @param player The player who used the axe
 */
function executePlayerVerticalAttack(player: Player): void {
  // Get direction vector directly from the player's view direction
  const viewDirection = player.getViewDirection();
  const dirX: number = viewDirection.x;
  const dirZ: number = viewDirection.z;

  // Origin at the player's current position
  const originPos: Vector3 = { x: player.location.x, y: player.location.y, z: player.location.z };

  // Summon a single long rock centered on the sweep (~6 blocks ahead)
  const midDistance: number = 1;
  const midPos: Vector3 = { x: originPos.x + dirX * midDistance, y: originPos.y, z: originPos.z + dirZ * midDistance };
  const rockSpawnPos: Vector3 = findGroundPosition(player.dimension, midPos) ?? midPos;

  // Get the player's actual Y rotation directly instead of calculating from view direction
  const playerRotation = player.getRotation();
  const yRotation: number = playerRotation.y;

  try {
    const rock = player.dimension.spawnEntity("ptd_bb:rock", rockSpawnPos);

    // Set server-side rotation (x=head tilt, y=body rotation)
    rock.setRotation({ x: 0, y: yRotation });

    player.dimension.playSound("item.trident.throw", rockSpawnPos);
    player.dimension.playSound("random.explode", rockSpawnPos);
  } catch (e) {
    // Ignore spawn errors to keep flow smooth
  }

  // Raycast-like sweep: 5 steps, 2-block spacing, 2-block radius
  const steps: number = 5;
  const baseDistance: number = 2;
  const spacing: number = 2;
  const radius: number = 2;
  const rockDamage: number = PIGLIN_CHAMPION_ATTACK_DAMAGES.vertical.rocks.damage;

  for (let i = 0; i < steps; i++) {
    const d: number = baseDistance + i * spacing;
    const stepPos: Vector3 = { x: originPos.x + dirX * d, y: originPos.y, z: originPos.z + dirZ * d };
    const center: Vector3 = findGroundPosition(player.dimension, stepPos) ?? stepPos;

    const entities = player.dimension.getEntities({
      location: center,
      maxDistance: radius,
      excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
      excludeFamilies: ["rock"],
    });

    for (const entity of entities) {
      if (entity.id === player.id) continue;

      const horizontalStrength: number = 1.7;
      const verticalStrength: number = 1.2;
      try {
        if (entity instanceof Player) {
          const gameMode = entity.getGameMode();
          if (gameMode === GameMode.Survival || gameMode === GameMode.Adventure) {
            entity.applyKnockback({ x: dirX * horizontalStrength, z: dirZ * horizontalStrength }, verticalStrength);
          }
        } else {
          entity.applyKnockback({ x: dirX * horizontalStrength, z: dirZ * horizontalStrength }, verticalStrength);
        }
      } catch (err) {
        const impulse: Vector3 = { x: dirX * horizontalStrength, y: verticalStrength, z: dirZ * horizontalStrength };
        entity.applyImpulse(impulse);
      }

      entity.applyDamage(rockDamage, { cause: EntityDamageCause.entityAttack, damagingEntity: player });
    }
  }

  // Damage the axe itemstack using the reusable module
  damageItemstack(player, "ptd_bb:piglin_champion_axe", 10, 20);
}


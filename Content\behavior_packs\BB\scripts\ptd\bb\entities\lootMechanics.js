import { applyDeathCameraShake, handleDeathMechanics } from "../bosses/general_mechanics/deathMechanics";
import { spawnItemFountain } from "../bosses/general_mechanics/itemFountain";
import { stopPiglinChampionSounds } from "../bosses/piglin_champion/soundManager";
import { stopNecromancerSounds } from "../bosses/necromancer/soundManager";
import { stopVoidHydraSounds } from "../bosses/void_hydra/soundManager";
export function handleLootMechanics(entity, currentTick) {
    const entityTypeId = entity.typeId;
    switch (entityTypeId) {
        case "ptd_bb:necromancer":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Necromancer
                duration: 150,
                xpOrbs: { count: 8, duration: 100, heightOffset: 2.25 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.ptd_bb_necromancer.death",
                // Add custom event to spawn essence fountain at the beginning of death sequence
                customEvents: [
                    {
                        tick: 1,
                        callback: (entity) => {
                            applyDeathCameraShake(entity, 150);
                            // Spawn 32 essence items in a fountain-like effect
                            spawnItemFountain(entity, "ptd_bb:necromancer_essence", 32, {
                                heightOffset: 2.25,
                                particleEffect: "minecraft:large_explosion",
                                soundEffect: "random.pop",
                                minVerticalStrength: 0.1,
                                maxVerticalStrength: 0.3,
                                minHorizontalStrength: 0.05,
                                maxHorizontalStrength: 0.2
                            });
                        }
                    }
                ],
                // Provide the sound stopping function
                stopSoundsFn: (entity, excludedSound) => stopNecromancerSounds(entity, undefined, excludedSound)
            }, currentTick) // Pass currentTick parameter (1 for first tick)
            ) {
                // If death mechanics were applied, return early
                return;
            }
            break;
        case "ptd_bb:winged_zombie":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Winged Zombie
                duration: 36,
                xpOrbs: { count: 1, duration: 10, heightOffset: 1 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.zombie.death",
                // Basic stopping function, as zombie minions don't have special sounds
                stopSoundsFn: () => {
                    /* No special sounds to stop */
                }
            }, currentTick)) {
                try {
                    // Create a small fountain of rotten flesh if this is tick 1 of death
                    if (currentTick === 1) {
                        // Create a fountain of 2 rotten flesh
                        spawnItemFountain(entity, "minecraft:rotten_flesh", 2, { heightOffset: 1, soundEffect: "random.pop" });
                    }
                }
                catch (e) { }
            }
            break;
        case "ptd_bb:zombie_brute":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Zombie Brute
                duration: 36,
                xpOrbs: { count: 1, duration: 15, heightOffset: 1 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.zombie.death",
                // Basic stopping function, as zombie minions don't have special sounds
                stopSoundsFn: () => {
                    /* No special sounds to stop */
                }
            }, currentTick)) {
                try {
                    // Create a larger fountain of rotten flesh if this is tick 1 of death
                    if (currentTick === 1) {
                        // Create a fountain of 3-4 rotten flesh (using two fountains for randomness)
                        spawnItemFountain(entity, "minecraft:rotten_flesh", 3, { heightOffset: 1, soundEffect: "random.pop" });
                        // 50% chance for an extra piece of rotten flesh
                        if (Math.random() < 0.5) {
                            spawnItemFountain(entity, "minecraft:rotten_flesh", 1, { heightOffset: 1 });
                        }
                    }
                }
                catch (e) { }
            }
            break;
        case "ptd_bb:skeleton_soul":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Skeleton Soul
                duration: 20,
                xpOrbs: { count: 1, duration: 8, heightOffset: 1 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.wither_skeleton.death",
                // Basic stopping function, as skeleton minions don't have special sounds
                stopSoundsFn: () => {
                    /* No special sounds to stop */
                }
            }, currentTick)) {
                try {
                    // Create a small fountain of bones if this is tick 1 of death
                    if (currentTick === 1) {
                        // Create a fountain of 1-2 bones
                        spawnItemFountain(entity, "minecraft:bone", 1, { heightOffset: 1, soundEffect: "random.pop" });
                        // 70% chance for an extra bone
                        if (Math.random() < 0.7) {
                            spawnItemFountain(entity, "minecraft:bone", 1, { heightOffset: 1 });
                        }
                        // 15% chance for a small soul item (if available, otherwise coal)
                        if (Math.random() < 0.15) {
                            spawnItemFountain(entity, "minecraft:coal", 1, { heightOffset: 1.2, soundEffect: "random.orb" });
                        }
                    }
                }
                catch (e) { }
            }
            break;
        case "ptd_bb:piglin_champion":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Piglin Champion
                duration: 100,
                xpOrbs: { count: 8, duration: 30, heightOffset: 2.25 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.ptd_bb_piglin_champion.death",
                // Add custom event to spawn essence fountain at the beginning of death sequence
                customEvents: [
                    {
                        tick: 1,
                        callback: (entity) => {
                            entity.dimension.spawnParticle("ptd_bb:pg_die1_01", entity.location);
                            // Spawn 32 essence items in a fountain-like effect
                            spawnItemFountain(entity, "ptd_bb:gold_scrap", 32, {
                                heightOffset: 2.25,
                                particleEffect: "minecraft:large_explosion",
                                soundEffect: "random.pop",
                                minVerticalStrength: 0.1,
                                maxVerticalStrength: 0.3,
                                minHorizontalStrength: 0.05,
                                maxHorizontalStrength: 0.2
                            });
                        }
                    }
                ],
                // Provide the sound stopping function
                stopSoundsFn: stopPiglinChampionSounds
            }, currentTick)) {
                // Death mechanics were applied successfully
            }
            break;
        case "ptd_bb:piglin_brute":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Piglin Brute
                duration: 40,
                xpOrbs: { count: 2, duration: 10, heightOffset: 1 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.piglin.death",
                // Basic stopping function, as piglin minions don't have special sounds
                stopSoundsFn: () => {
                    /* No special sounds to stop */
                }
            }, currentTick)) {
                try {
                    // Create a fountain of gold nuggets if this is tick 1 of death
                    if (currentTick === 1) {
                        // Create a fountain of 2-3 gold nuggets
                        spawnItemFountain(entity, "minecraft:gold_nugget", 2, { heightOffset: 1, soundEffect: "random.pop" });
                        // 60% chance for an extra gold nugget
                        if (Math.random() < 0.6) {
                            spawnItemFountain(entity, "minecraft:gold_nugget", 1, { heightOffset: 1 });
                        }
                        // 20% chance for a gold ingot
                        if (Math.random() < 0.2) {
                            spawnItemFountain(entity, "minecraft:gold_ingot", 1, { heightOffset: 1.2, soundEffect: "random.orb" });
                        }
                    }
                }
                catch (e) { }
            }
            break;
        case "ptd_bb:piglin_marauder":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Piglin Marauder
                duration: 52, // 1.0417 seconds * 50 = ~52 ticks
                xpOrbs: { count: 3, duration: 10, heightOffset: 1.5 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.piglin.death",
                // Basic stopping function, as piglin minions don't have special sounds
                stopSoundsFn: () => {
                    /* No special sounds to stop */
                }
            }, currentTick)) {
                try {
                    // Create a fountain of gold items if this is tick 1 of death
                    if (currentTick === 1) {
                        // Create a fountain of 3-4 gold nuggets
                        spawnItemFountain(entity, "minecraft:gold_nugget", 3, { heightOffset: 1.5, soundEffect: "random.pop" });
                        // 80% chance for an extra gold nugget
                        if (Math.random() < 0.8) {
                            spawnItemFountain(entity, "minecraft:gold_nugget", 1, { heightOffset: 1.5 });
                        }
                        // 40% chance for a gold ingot
                        if (Math.random() < 0.4) {
                            spawnItemFountain(entity, "minecraft:gold_ingot", 1, { heightOffset: 1.7, soundEffect: "random.orb" });
                        }
                        // 15% chance for an additional gold ingot (rare drop)
                        if (Math.random() < 0.15) {
                            spawnItemFountain(entity, "minecraft:gold_ingot", 1, { heightOffset: 1.9, soundEffect: "random.levelup" });
                        }
                    }
                }
                catch (e) { }
            }
            break;
        case "ptd_bb:void_hydra":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Void Hydra
                duration: 500,
                xpOrbs: { count: 8, duration: 100, heightOffset: 2.25 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.ptd_bb_void_hydra.death",
                // Add custom event to spawn essence fountain at the beginning of death sequence
                customEvents: [
                    {
                        tick: 1,
                        callback: (entity) => {
                            applyDeathCameraShake(entity, 150);
                            // Spawn 32 essence items in a fountain-like effect
                            spawnItemFountain(entity, "ptd_bb:void_hydra_essence", 32, {
                                heightOffset: 2.25,
                                particleEffect: "minecraft:large_explosion",
                                soundEffect: "random.pop",
                                minVerticalStrength: 0.1,
                                maxVerticalStrength: 0.3,
                                minHorizontalStrength: 0.05,
                                maxHorizontalStrength: 0.2
                            });
                        }
                    }
                ],
                // Provide the sound stopping function
                stopSoundsFn: (entity, excludedSound) => stopVoidHydraSounds(entity, excludedSound)
            }, currentTick) // Pass currentTick parameter (1 for first tick)
            ) {
                // If death mechanics were applied, return early
                return;
            }
            break;
        case "ptd_bb:void_hydra_right_head":
            if (handleDeathMechanics(entity, {
                // Configure death mechanics specific to the Void Hydra
                duration: 500,
                xpOrbs: { count: 2, duration: 40, heightOffset: 4.25 },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.ptd_bb_void_hydra.death",
                // Add custom event to spawn essence fountain at the beginning of death sequence
                customEvents: [
                    {
                        tick: 1,
                        callback: (entity) => {
                            applyDeathCameraShake(entity, 20);
                            // Spawn 32 essence items in a fountain-like effect
                            spawnItemFountain(entity, "ptd_bb:void_hydra_essence", 8, {
                                heightOffset: 4.25,
                                particleEffect: "minecraft:large_explosion",
                                soundEffect: "random.pop",
                                minVerticalStrength: 0.1,
                                maxVerticalStrength: 0.3,
                                minHorizontalStrength: 0.05,
                                maxHorizontalStrength: 0.2
                            });
                        }
                    }
                ]
            }, currentTick, false) // Pass currentTick parameter (1 for first tick)
            ) {
                // If death mechanics were applied, return early
                return;
            }
            break;
        default:
            break;
    }
}

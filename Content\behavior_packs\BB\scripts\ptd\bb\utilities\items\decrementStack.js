import { ItemStack, system } from "@minecraft/server";
// Function to decrement the item stack in the player's main hand
export async function decrementStackMainHand(mainHand, mainHandItem) {
    try {
        // Decrease the item count in the main hand by 1
        mainHand.setItem(new ItemStack(mainHandItem.type.id, mainHandItem.amount - 1));
        await system.waitTicks(1);
    }
    catch {
        // If an error occurs, set the main hand item to air (empty slot)
        mainHand.setItem(new ItemStack(`minecraft:air`));
    }
    return;
}

import { Dimension, Entity, EntityComponentTypes, system, Vector3, world } from "@minecraft/server";
import { handleFlyingSkullHit } from "../bosses/necromancer/flyingSkull";

// entityRemove beforeEvent listener for projectiles
world.beforeEvents.entityRemove.subscribe(async (event) => {
  try {
    if (!event.removedEntity) return;

    // Check if the entity is a projectile
    if (event.removedEntity.getComponent(EntityComponentTypes.Projectile)) {
      // Check if the projectile is a flying skull
      if (event.removedEntity.typeId === "ptd_bb:flying_skull") {
        const eventData: ProjectileHitEvent = {
          projectile: event.removedEntity,
          dimension: event.removedEntity.dimension,
          location: event.removedEntity.location,
          tags: event.removedEntity.getTags()
        };

        await system.waitTicks(3); // Wait for 1 tick
        handleProjectileHitMechanics(eventData);
      }
    }
  } catch (error) {
    return;
  }
  return;
});

// Custom projectile hit data for entityRemove beforeEvent listener
export interface ProjectileHitEvent {
  projectile: Entity;
  dimension: Dimension;
  location: Vector3;
  tags: string[];
}

/**
 *
 * @param projectile The projectile entity
 * @returns
 */
function handleProjectileHitMechanics(event: ProjectileHitEvent): void {
  try {
    if (!event.projectile) return;

    const projectileTypeId = event.projectile.typeId;

    switch (projectileTypeId) {
      case "ptd_bb:flying_skull":
        let run = system.run(() => {
          handleFlyingSkullHit(event);
          system.clearRun(run);
        });
        break;
      default:
        break;
    }
  } catch (error) {
    return;
  }
  return;
}

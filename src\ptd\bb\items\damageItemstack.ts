import { EntityComponentTypes, EntityEquippableComponent, EquipmentSlot, ItemComponentTypes, Player } from "@minecraft/server";

/**
 * Damages an itemstack by a specified amount
 * @param player The player holding the item
 * @param itemTypeId The type ID of the item to damage
 * @param minDamage The minimum damage to apply
 * @param maxDamage The maximum damage to apply (inclusive)
 * @returns True if the item was successfully damaged, false otherwise
 */
export function damageItemstack(player: Player, itemTypeId: string, minDamage: number, maxDamage: number): boolean {
  try {
    // Get the player's current equipped item
    const equippableComponent = player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent;
    if (!equippableComponent) return false;

    const mainHandItem = equippableComponent.getEquipment(EquipmentSlot.Mainhand);
    if (!mainHandItem || mainHandItem.type.id !== itemTypeId) return false;

    // Get the durability component
    const durabilityComponent = mainHandItem.getComponent(ItemComponentTypes.Durability);
    if (!durabilityComponent) return false;

    // Generate a random damage amount between minDamage and maxDamage
    const range = maxDamage - minDamage + 1;
    const damageAmount = Math.floor(Math.random() * range) + minDamage;

    // Apply damage to the item
    durabilityComponent.damage += damageAmount;

    // Update the item in the player's hand
    equippableComponent.setEquipment(EquipmentSlot.Mainhand, mainHandItem);

    return true;
  } catch (error) {
    console.warn(`Error damaging itemstack: ${error}`);
    return false;
  }
}

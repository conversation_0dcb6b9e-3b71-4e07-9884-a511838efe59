import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system } from "@minecraft/server";
import { getDirection } from "../../../utilities/vector3";
import { NON_SOLID_BLOCKS } from "../../../utilities/constants/nonSolidBlocks";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Checks if a block at the given location is an air block or a non-solid block
 * If it's a non-solid block, breaks it to simulate physics with particles
 * @param dimension The dimension to check in
 * @param location The location to check
 * @returns True if the block is an air block or a non-solid block, false otherwise
 */
function isAirOrNonSolid(dimension, location) {
    const block = dimension.getBlock(location);
    if (!block)
        return true; // Treat as air if block is null
    if (block.isAir)
        return true;
    // If it's a non-solid block, break it to simulate physics
    if (NON_SOLID_BLOCKS.has(block.type.id)) {
        try {
            // Break the block using the setblock command with destroy flag for particles
            const x = Math.floor(location.x);
            const y = Math.floor(location.y);
            const z = Math.floor(location.z);
            dimension.runCommand(`setblock ${x} ${y} ${z} air [] destroy`);
        }
        catch (error) {
            console.warn(`Error breaking non-solid block with command: ${error}`);
            // Fallback to using setType if the command fails
            try {
                block.setType("minecraft:air");
            }
            catch (fallbackError) {
                console.warn(`Fallback error breaking non-solid block: ${fallbackError}`);
            }
        }
        return true;
    }
    return false;
}
/**
 * Finds a position just above the ground (solid block) at the given x,z coordinates
 * @param dimension The dimension to check in
 * @param location The base location to check
 * @param maxSearchDistance Maximum vertical distance to search
 * @returns A location 0.001 blocks above a solid block, or undefined if none found
 */
function findGroundPosition(dimension, location, maxSearchDistance = 20) {
    // Start from a higher position to ensure we find the ground in most cases
    const startY = location.y + 10;
    // Search downward for a solid block
    for (let y = 0; y <= maxSearchDistance * 2; y++) {
        const checkPos = { x: location.x, y: startY - y, z: location.z };
        // Get the block at this position
        const block = dimension.getBlock(checkPos);
        // If we found a non-air block
        if (block && !block.isAir) {
            // Check if it's a non-solid block (grass, flower, etc.)
            if (NON_SOLID_BLOCKS.has(block.type.id)) {
                // Instead of returning, continue searching downward
                continue;
            }
            // If it's a solid block, check if the block above is air or non-solid
            const blockAbovePos = { x: location.x, y: startY - y + 1, z: location.z };
            // Only return this position if the block above is air or a non-solid block
            if (isAirOrNonSolid(dimension, blockAbovePos)) {
                return {
                    x: location.x,
                    y: startY - y + 1.001, // Position slightly above the solid block
                    z: location.z
                };
            }
            // If the block above isn't air or non-solid, continue searching downward
        }
    }
    // If we couldn't find a suitable ground position, return the original location
    return location;
}
/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 44;
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 106;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the vertical attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
export function executeVerticalAttack(piglinChampion, target) {
    // Wait for the attack timing before executing the attack
    let timing = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(timing);
                return;
            }
            if (piglinChampion.getProperty("ptd_bb:attack") === "vertical") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                if (currentTarget) {
                    performVerticalAttack(piglinChampion, currentTarget);
                }
            }
        }
        catch (error) {
            system.clearRun(timing);
        }
    }, ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_bb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "vertical" - prevents interference with stuns
            if (currentAttack === "vertical") {
                piglinChampion.triggerEvent("ptd_bb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_bb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Performs the actual vertical attack damage and effects
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
function performVerticalAttack(piglinChampion, target) {
    // Forward direction from piglin to target (normalized x/z)
    const forward = getDirection(piglinChampion.location, target.location);
    const dirX = forward.x;
    const dirZ = forward.z;
    // Use piglin's current location as origin
    const originPos = { x: piglinChampion.location.x, y: piglinChampion.location.y, z: piglinChampion.location.z };
    // Summon a single long rock roughly centered on the sweep (at 6 blocks ahead)
    const midDistance = 6;
    const midPos = { x: originPos.x + dirX * midDistance, y: originPos.y, z: originPos.z + dirZ * midDistance };
    const rockSpawnPos = findGroundPosition(piglinChampion.dimension, midPos) ?? midPos;
    // Get the piglin champion's actual Y rotation directly instead of calculating from direction
    const piglinRotation = piglinChampion.getRotation();
    const yRotation = piglinRotation.y;
    try {
        const rock = piglinChampion.dimension.spawnEntity("ptd_bb:rock", rockSpawnPos);
        // Set server-side rotation (x=head tilt, y=body rotation)
        rock.setRotation({ x: 0, y: yRotation });
        piglinChampion.dimension.playSound("item.trident.throw", rockSpawnPos);
    }
    catch (e) {
        // ignore spawn errors
    }
    // Raycast-like sweep: 5 steps, 2 blocks apart, 2 block radius
    const steps = 5;
    const baseDistance = 2;
    const spacing = 2;
    const radius = 2;
    const rockDamage = PIGLIN_CHAMPION_ATTACK_DAMAGES.vertical.rocks.damage;
    for (let i = 0; i < steps; i++) {
        const d = baseDistance + i * spacing;
        const stepPos = { x: originPos.x + dirX * d, y: originPos.y, z: originPos.z + dirZ * d };
        const center = findGroundPosition(piglinChampion.dimension, stepPos) ?? stepPos;
        const entities = piglinChampion.dimension.getEntities({
            location: center,
            maxDistance: radius,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["piglin_champion", "piglin", "rock"],
        });
        for (const entity of entities) {
            const horizontalStrength = 1.7;
            const verticalStrength = 1.2;
            try {
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.Survival || gameMode === GameMode.Adventure) {
                        entity.applyKnockback({ x: dirX * horizontalStrength, z: dirZ * horizontalStrength }, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback({ x: dirX * horizontalStrength, z: dirZ * horizontalStrength }, verticalStrength);
                }
            }
            catch (err) {
                const impulse = { x: dirX * horizontalStrength, y: verticalStrength, z: dirZ * horizontalStrength };
                entity.applyImpulse(impulse);
            }
            entity.applyDamage(rockDamage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
        }
    }
}

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { getDistance } from "../../../../../utilities/vector3";

/**
 * When the attack should apply in the animation
 */
const ATTACK_TIMING = 35; // Apply damage at tick 35

/**
 * The total animation time in ticks
 */
const ANIMATION_TIME = 88; // Total animation time in ticks

/**
 * Cooldown before executing the next attack
 */
const COOLDOWN_TIME = 20;

/**
 * Normalizes a 2D direction vector (x and z only)
 *
 * @param direction Vector to normalize
 * @returns Normalized 2D vector
 */
function normalizeDirection(x: number, z: number): { x: number; z: number } {
  const length = Math.sqrt(x * x + z * z);
  if (length > 0) {
    return { x: x / length, z: z / length };
  }
  return { x: 0, z: 0 };
}

/**
 * Executes the splash attack for the Zombie Brute
 * Applies damage and knockback to nearby entities based on the direction to target
 *
 * @param zombieBrute The zombie brute entity
 */
export function executeSplashAttack(zombieBrute: Entity): void {
  // Apply damage to nearby entities
  const damageRadius = 3;
  // Use the brute's attack damage (8)
  const damage = 8;

  // Wait for the attack timing before executing the attack
  let timing = system.runTimeout(() => {
    try {
      const isDead = zombieBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }
      // Find the target
      const target = getTarget(zombieBrute, zombieBrute.location, 32, ["necromancer"]);

      // Get direction vector based on target or fallback to view direction
      let dirX = 0;
      let dirZ = 0;

      if (target) {
        // Calculate direction vector from brute to target
        const dx = target.location.x - zombieBrute.location.x;
        const dz = target.location.z - zombieBrute.location.z;

        // Normalize the direction
        const normalizedDir = normalizeDirection(dx, dz);
        dirX = normalizedDir.x;
        dirZ = normalizedDir.z;
      } else {
        // Fallback to view direction if no target found
        const viewDirection = zombieBrute.getViewDirection();
        dirX = viewDirection.x;
        dirZ = viewDirection.z;
      }

      // Calculate position 2.3 blocks in front of the brute in the direction of the target
      const originPos: Vector3 = { x: zombieBrute.location.x + dirX * 2.3, y: zombieBrute.location.y, z: zombieBrute.location.z + dirZ * 2.3 };

      // Play attack impact sound and particles
      zombieBrute.dimension.spawnParticle("minecraft:large_explosion", originPos);
      zombieBrute.dimension.playSound("mob.zombie.wood", originPos, { volume: 1.5, pitch: 0.8 });

      // Find entities within the damage radius
      zombieBrute.dimension
        .getEntities({
          location: originPos,
          maxDistance: damageRadius,
          excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
          excludeFamilies: ["zombie_brute", "undead", "necromancer"]
        })
        .forEach((entity) => {
          // Use brute's direction for knockback
          // Create 2D points (same y-coordinate) to calculate horizontal distance
          const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
          const point2: Vector3 = { x: originPos.x, y: 0, z: originPos.z };
          const distance = getDistance(point1, point2);

          if (distance > 0) {
            // Use the direction for knockback
            const nx = dirX;
            const nz = dirZ;

            // Splash attack parameters
            const horizontalStrength = 2.0;
            const verticalStrength = 0.5;

            try {
              // Try to apply knockback first
              if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                  entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
              } else {
                entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
              }
            } catch (e) {
              // Fallback to applyImpulse if applyKnockback fails
              const impulse: Vector3 = { x: nx * horizontalStrength, y: verticalStrength, z: nz * horizontalStrength };

              entity.applyImpulse(impulse);
            }
          }

          // Apply damage after knockback/impulse
          entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: zombieBrute });
        });
      // Clear the timeout
      system.clearRun(timing);
    } catch (error) {
      // handle error silently
      system.clearRun(timing);
    }
  }, ATTACK_TIMING);

  // Reset the attack state to "none" after the animation is complete
  let reset = system.runTimeout(() => {
    try {
      const isDead = zombieBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }
      zombieBrute.triggerEvent("ptd_bb:reset_attack");
      system.clearRun(reset);
    } catch (error) {
      // handle error silently
      system.clearRun(reset);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to select the next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = zombieBrute.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }
      zombieBrute.setProperty("ptd_bb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      // handle error silently
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
  return;
}

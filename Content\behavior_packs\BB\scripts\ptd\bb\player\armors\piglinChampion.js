import { Player, system, EntityDamageCause, GameMode, EntityComponentTypes } from "@minecraft/server";
import { cameraShake } from "../../bosses/general_effects/camerashake";
/**
 * Map to track crouch states for each player
 */
const playerCrouchStates = new Map();
/**
 * Constants for piglin champion armor mechanics
 */
const SLAM_CONSTANTS = {
    /** Minimum crouch time in ticks before slam can be triggered */
    MIN_CROUCH_TIME: 10,
    /** Slam damage radius */
    DAMAGE_RADIUS: 8,
    /** Slam damage amount */
    DAMAGE: 12,
    /** Slam knockback power */
    KNOCKBACK_POWER: 2.0,
    /** Cooldown time in ticks */
    COOLDOWN_TIME: 100 // 5 seconds
};
/**
 * Handles piglin champion armor set mechanics for a player
 * @param player The player wearing the piglin champion armor set
 */
export function handlePiglinChampionArmor(player) {
    const playerId = player.id;
    // Initialize player state if not exists
    if (!playerCrouchStates.has(playerId)) {
        playerCrouchStates.set(playerId, { isCrouching: false, crouchStartTime: 0, shouldTriggerSlam: false, slamCooldown: false, cooldownEndTime: 0 });
    }
    const state = playerCrouchStates.get(playerId);
    const currentTick = system.currentTick;
    // Check cooldown
    if (state.slamCooldown && currentTick >= state.cooldownEndTime) {
        state.slamCooldown = false;
    }
    // Skip if on cooldown
    if (state.slamCooldown) {
        return;
    }
    const isCrouchingNow = player.isSneaking;
    // Handle crouch state changes
    if (isCrouchingNow && !state.isCrouching) {
        // Started crouching
        state.isCrouching = true;
        state.crouchStartTime = currentTick;
    }
    else if (!isCrouchingNow && state.isCrouching) {
        // Stopped crouching - check if we should trigger slam
        const crouchDuration = currentTick - state.crouchStartTime;
        if (crouchDuration >= SLAM_CONSTANTS.MIN_CROUCH_TIME) {
            // Player released crouch after minimum time - trigger slam immediately
            state.shouldTriggerSlam = true;
        }
        state.isCrouching = false;
    }
    // Execute slam attack if triggered
    if (state.shouldTriggerSlam) {
        executeSlamAttack(player);
        state.shouldTriggerSlam = false;
        state.slamCooldown = true;
        state.cooldownEndTime = currentTick + SLAM_CONSTANTS.COOLDOWN_TIME;
    }
}
/**
 * Executes the slam attack when player impacts the ground
 * @param player The player performing the slam
 */
function executeSlamAttack(player) {
    const playerLocation = player.location;
    // Play body slam particle effect (same as piglin champion)
    player.dimension.spawnParticle("ptd_bb:pg_foot_stomp1_01", playerLocation);
    // Play sound effects
    player.dimension.playSound("random.explode", playerLocation, { volume: 1.5, pitch: 0.8 });
    // Apply shockwave effect (excluding tamed entities)
    applyPiglinChampionShockwave(player, SLAM_CONSTANTS.DAMAGE_RADIUS, SLAM_CONSTANTS.KNOCKBACK_POWER, SLAM_CONSTANTS.DAMAGE, ["piglin_champion", "piglin"] // Exclude piglin families
    );
    // Apply camera shake effect
    cameraShake(player, 32, 0.02, 0.5, 0.5);
    // Play additional explosion particle
    player.dimension.spawnParticle("minecraft:large_explosion", playerLocation);
}
/**
 * Custom shockwave function that excludes tamed entities from damage and knockback
 * @param source The entity causing the shockwave
 * @param radius The radius of the shockwave effect
 * @param power The power of the knockback (1.0 is standard, higher values increase strength)
 * @param damage The amount of damage to apply to affected entities (0 for no damage)
 * @param excludedFamilies Array of entity family types to exclude from the effect
 */
function applyPiglinChampionShockwave(source, radius, power = 1.0, damage = 0, excludedFamilies = []) {
    const sourceLocation = source.location;
    // Create query options to exclude specified families
    const queryOptions = { location: sourceLocation, maxDistance: radius, excludeFamilies: [...excludedFamilies, "inanimate"] };
    // Apply knockback and damage to entities within the shockwave radius
    source.dimension.getEntities(queryOptions).forEach((entity) => {
        if (entity === source)
            return; // Skip the source entity
        // Check if entity is tamed and skip if it is
        const tameableComponent = entity.getComponent(EntityComponentTypes.Tameable);
        if (tameableComponent && tameableComponent.isTamed) {
            return; // Skip tamed entities
        }
        const entityLocation = entity.location;
        // Calculate the difference in coordinates
        const dx = entityLocation.x - sourceLocation.x;
        const dz = entityLocation.z - sourceLocation.z;
        // Calculate the distance
        const distance = Math.sqrt(dx * dx + dz * dz);
        if (distance <= 0)
            return; // Avoid division by zero
        // Calculate strength inversely proportional to distance
        const strengthMultiplier = (radius - distance) / radius;
        const horizontalStrength = strengthMultiplier * (4 * power - 1) + 1;
        const verticalStrength = strengthMultiplier * (3 * power - 0.5) + 0.5;
        // Normalize the direction vector
        const nx = dx / distance;
        const nz = dz / distance;
        // Apply knockback
        try {
            // Calculate the impulse vector
            const impulse = { x: nx * horizontalStrength, y: verticalStrength, z: nz * horizontalStrength };
            entity.applyImpulse(impulse);
        }
        catch (e) {
            // Fallback to applyKnockback if applyImpulse fails
            const knockbackDirection = { x: nx * horizontalStrength, z: nz * horizontalStrength };
            if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.Survival || gameMode === GameMode.Adventure) {
                    entity.applyKnockback(knockbackDirection, verticalStrength);
                }
            }
            else {
                entity.applyKnockback(knockbackDirection, verticalStrength);
            }
        }
        // Apply damage if specified and entity is not an XP orb or item
        if (damage > 0 && entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
            entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
        }
    });
}
/**
 * Cleanup function to remove player state when they disconnect
 * @param playerId The ID of the player to clean up
 */
export function cleanupPlayerState(playerId) {
    playerCrouchStates.delete(playerId);
}

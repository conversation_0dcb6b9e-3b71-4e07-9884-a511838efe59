import { piglinChampionAxeOnUse } from "./weapons/piglinChampionAxe";
import { piglinChampionGobletOnUse } from "./other/piglinChampionGoblet";
/**
 * Global Item Custom Component Initialization
 * Registers the ptd_bb:item custom component for all Better Bosses items
 *
 * Note: This component is referenced in item JSON files. If you see warnings about
 * "Component 'ptd_bb:item' was not registered", it's because items existed in the world
 * before this registration completed. This is harmless and will resolve on world reload.
 */
export function initializeItemCustomComponents(data) {
    try {
        data.registerCustomComponent("ptd_bb:item", {
            onUse(ev) {
                const item = ev.itemStack;
                const player = ev.source;
                // Skip if no item or player
                if (!item || !player)
                    return;
                const itemTypeId = item.type.id;
                switch (itemTypeId) {
                    case "ptd_bb:piglin_champion_axe":
                        piglinChampionAxeOnUse(player, item);
                        break;
                    case "ptd_bb:piglin_champion_goblet":
                        piglinChampionGobletOnUse(player, item);
                        break;
                    default:
                        break;
                }
                return;
            }
        });
        console.log("[Better Bosses] Successfully registered ptd_bb:item custom component");
    }
    catch (error) {
        console.error("[Better Bosses] Failed to register ptd_bb:item custom component:", error);
    }
}

import { <PERSON>ti<PERSON>, <PERSON><PERSON>tyDamage<PERSON><PERSON><PERSON>, <PERSON>tityQueryO<PERSON>s, GameMode, Player, Vector3 } from "@minecraft/server";

/**
 * Creates a shockwave effect that applies knockback and damage to entities within a radius
 * @param source The entity causing the shockwave
 * @param radius The radius of the shockwave effect
 * @param power The power of the knockback (1.0 is standard, higher values increase strength)
 * @param damage The amount of damage to apply to affected entities (0 for no damage)
 * @param excludedFamilies Array of entity family types to exclude from the effect
 */
export function shockwave(source: Entity, radius: number, power: number = 1.0, damage: number = 0, excludedFamilies: string[] = []): void {
  const sourceLocation = source.location;

  // Create query options to exclude specified families
  const queryOptions: EntityQueryOptions = { location: sourceLocation, maxDistance: radius, excludeFamilies: [...excludedFamilies, "inanimate"] };

  // Apply knockback and damage to entities within the shockwave radius
  source.dimension.getEntities(queryOptions).forEach((entity) => {
    if (entity === source) return; // Skip the source entity

    const entityLocation = entity.location;

    // Calculate the difference in coordinates
    const dx = entityLocation.x - sourceLocation.x;
    const dz = entityLocation.z - sourceLocation.z;

    // Calculate the distance
    const distance = Math.sqrt(dx * dx + dz * dz);
    if (distance <= 0) return; // Avoid division by zero

    // Calculate strength inversely proportional to distance
    const strengthMultiplier = (radius - distance) / radius;
    const horizontalStrength = strengthMultiplier * (4 * power - 1) + 1;
    const verticalStrength = strengthMultiplier * (2 * power - 0.5) + 0.5;

    // Normalize the direction vector
    const nx = dx / distance;
    const nz = dz / distance;

    // Apply knockback
    try {
      // Calculate the impulse vector
      const impulse: Vector3 = { x: nx * horizontalStrength, y: verticalStrength, z: nz * horizontalStrength };

      entity.applyImpulse(impulse);
    } catch (e) {
      // Fallback to applyKnockback if applyImpulse fails
      if (entity instanceof Player) {
        const gameMode = entity.getGameMode();
        if (gameMode === GameMode.Survival || gameMode === GameMode.Adventure) {
          entity.applyKnockback({ x: nx * horizontalStrength, z: nz * horizontalStrength }, verticalStrength);
        }
      } else {
        entity.applyKnockback({ x: nx * horizontalStrength, z: nz * horizontalStrength }, verticalStrength);
      }
    }

    // Apply damage if specified and entity is not an XP orb or item
    if (damage > 0 && entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
      entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
    }
  });
  return;
}

import { Player, EquipmentSlot, EntityComponentTypes, EntityEquippableComponent, GameMode } from "@minecraft/server";
import { handlePiglinChampionArmor } from "./piglinChampion";

/**
 * Interface defining a complete armor set configuration
 */
export interface ArmorSet {
  /** Helmet item identifier */
  head: string;
  /** Chestplate item identifier */
  chest: string;
  /** Leggings item identifier */
  legs: string;
  /** Boots item identifier */
  feet: string;
  /** Unique identifier for this armor set */
  id: string;
  /** Display name for this armor set */
  name: string;
}

/**
 * Interface for armor set detection result
 */
export interface ArmorSetDetection {
  /** Whether the player is wearing a complete armor set */
  isWearing: boolean;
  /** The armor set being worn, if any */
  armorSet?: ArmorSet;
}

/**
 * Available armor sets configuration
 */
export const ARMOR_SETS: ArmorSet[] = [
  {
    id: "piglin_champion",
    name: "Piglin Champion",
    head: "ptd_bb:piglin_champion_helmet",
    chest: "ptd_bb:piglin_champion_chestplate",
    legs: "ptd_bb:piglin_champion_leggings",
    feet: "ptd_bb:piglin_champion_boots"
  }
];

/**
 * Detects if a player is wearing a complete armor set
 * @param player The player to check
 * @returns ArmorSetDetection result
 */
export function detectArmorSet(player: Player): ArmorSetDetection {
  // Skip spectator players
  if (player.getGameMode() === GameMode.Spectator) {
    return { isWearing: false };
  }

  const equipmentComponent = player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent;
  if (!equipmentComponent) {
    return { isWearing: false };
  }

  // Get equipped armor pieces
  const headItem = equipmentComponent.getEquipment(EquipmentSlot.Head);
  const chestItem = equipmentComponent.getEquipment(EquipmentSlot.Chest);
  const legsItem = equipmentComponent.getEquipment(EquipmentSlot.Legs);
  const feetItem = equipmentComponent.getEquipment(EquipmentSlot.Feet);

  // Check each armor set
  for (const armorSet of ARMOR_SETS) {
    const isComplete =
      headItem?.typeId === armorSet.head &&
      chestItem?.typeId === armorSet.chest &&
      legsItem?.typeId === armorSet.legs &&
      feetItem?.typeId === armorSet.feet;

    if (isComplete) {
      return { isWearing: true, armorSet };
    }
  }

  return { isWearing: false };
}

/**
 * Main handler for armor set mechanics - called every tick for each player
 * @param player The player to handle armor sets for
 */
export function handleArmorSets(player: Player): void {
  const detection: ArmorSetDetection = detectArmorSet(player);

  if (!detection.isWearing || !detection.armorSet) {
    return;
  }

  // Handle specific armor set mechanics
  switch (detection.armorSet.id) {
    case "piglin_champion":
      handlePiglinChampionArmor(player);
      break;
    // Add more armor sets here as needed
  }
}

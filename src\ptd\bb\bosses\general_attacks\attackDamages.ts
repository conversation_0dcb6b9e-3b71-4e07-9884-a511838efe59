/**
 * Interface for attack damage configuration
 */
interface AttackDamage {
  /**
   * Direct damage value to apply
   */
  damage: number;
}

/**
 * Interface for Piglin Champion attack damages
 */
interface PiglinChampionAttackDamages {
  horizontal: AttackDamage;
  vertical: { axe: AttackDamage; rocks: AttackDamage };
  foot_stomp: AttackDamage;
  spin_slam: AttackDamage;
  body_slam: AttackDamage;
  charging: AttackDamage;
  summoning_chant?: AttackDamage; // Optional as it might not deal damage
}

/**
 * Interface for Necromancer attack damages
 */
interface NecromancerAttackDamages {
  cataclysm: AttackDamage;
  soul_drain: AttackDamage;
  // Add other attacks as they are implemented
}

interface GrimhowlAttackDamages {
  left_claw: AttackDamage;
  right_claw: AttackDamage;
  backstep_sword: AttackDamage;
  pounce: AttackDamage;
  shadow_onslaught: AttackDamage;
  slash: AttackDamage;
  spinning_slash: AttackDamage;
  roar: AttackDamage;
  collision_damage: AttackDamage; // Not all attacks have collision damage
}

/**
 * Interface for Void Hydra attack damages
 */
interface VoidHydraAttackDamages {
  right_atomic_cross: AttackDamage;
  right_atomic: AttackDamage;
  right_vacuum: AttackDamage;
  right_summon?: AttackDamage; // Optional as it might not deal direct damage
  mid_atomic: AttackDamage;
  mid_meteor: AttackDamage;
  mid_singularity: AttackDamage;
  left_atomic_cross: AttackDamage;
  left_atomic: AttackDamage;
  left_railgun: AttackDamage;
  left_missile: AttackDamage;
  left_shout: AttackDamage;
}

/**
 * Piglin Champion attack damages as direct values
 */
export const PIGLIN_CHAMPION_ATTACK_DAMAGES: PiglinChampionAttackDamages = {
  horizontal: { damage: 16 },
  vertical: { axe: { damage: 16 }, rocks: { damage: 8 } },
  foot_stomp: { damage: 12 },
  spin_slam: { damage: 12 },
  body_slam: { damage: 18 },
  charging: { damage: 7 }
};

/**
 * Necromancer attack damages as direct values
 */
export const NECROMANCER_ATTACK_DAMAGES: NecromancerAttackDamages = { cataclysm: { damage: 6 }, soul_drain: { damage: 7 } };

/**
 * Grimhowl attack damages as direct values
 */
export const GRIMHOWL_ATTACK_DAMAGES: GrimhowlAttackDamages = {
  left_claw: { damage: 4 },
  right_claw: { damage: 4 },
  backstep_sword: { damage: 5 },
  pounce: { damage: 8 },
  shadow_onslaught: { damage: 8 },
  slash: { damage: 8 },
  spinning_slash: { damage: 6 },
  roar: { damage: 2 },
  collision_damage: { damage: 4 }
};

/**
 * Void Hydra attack damages as direct values
 * Balanced for a high-tier boss with 1500 health
 */
export const VOID_HYDRA_ATTACK_DAMAGES: VoidHydraAttackDamages = {
  right_atomic_cross: { damage: 8 },
  right_atomic: { damage: 8 },
  right_vacuum: { damage: 8 }, // Continuous damage over time
  mid_atomic: { damage: 12 },
  mid_meteor: { damage: 18 },
  mid_singularity: { damage: 20 },
  left_atomic_cross: { damage: 14 },
  left_atomic: { damage: 12 },
  left_railgun: { damage: 22 }, // High damage, precise attack
  left_missile: { damage: 15 },
  left_shout: { damage: 10 } // Area effect with knockback
};

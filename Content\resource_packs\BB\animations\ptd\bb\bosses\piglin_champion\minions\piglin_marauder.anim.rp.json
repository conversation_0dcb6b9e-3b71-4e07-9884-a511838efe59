{"format_version": "1.8.0", "animations": {"animation.ptd_bb.piglin_maruarder.spawn": {"loop": "hold_on_last_frame", "animation_length": 1.125, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.0833": [4.57, 0, 0], "0.1667": [3.77, 0, 0], "0.25": [3.33, 0, 0], "0.3333": [4.01, 0, 0], "0.375": [5, 0, 0], "0.4167": [10.1, 0, 0], "0.5": [24.51, 0, 0], "0.5417": [27.5, 0, 0], "0.5833": [27.23, 0, 0], "0.6667": [25.25, 0, 0], "0.75": [21.86, 0, 0], "0.8333": [17.66, 0, 0], "0.9167": [13.22, 0, 0], "1.0": [9.14, 0, 0], "1.0833": [6.02, 0, 0], "1.125": [5, 0, 0]}, "position": {"0.0": [0, 10, 2], "0.0833": [0, 8.37, 2], "0.1667": [0, 5.99, 2.01], "0.25": [0, 3.32, 2.02], "0.3333": [0, 0.83, 2.02], "0.4167": [0, -1, 2], "0.5": [0, -3.45, 1.95], "0.5417": [0, -3.53, 1.74], "0.5833": [0, -3.46, 1.66], "0.6667": [0, -3.11, 1.43], "0.75": [0, -2.57, 1.16], "0.8333": [0, -1.92, 0.85], "0.9167": [0, -1.25, 0.55], "1.0": [0, -0.63, 0.28], "1.0833": [0, -0.16, 0.07], "1.125": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-110, 0, 0], "0.0833": [-115.22, 0, 0], "0.1667": [-123.4, 0, 0], "0.25": [-130.83, 0, 0], "0.3333": [-133.83, 0, 0], "0.375": [-132.5, 0, 0], "0.4167": [-115.04, 0, 0], "0.5": [-65, 0, 0], "0.5833": [-59.49, 0, 0], "0.6667": [-55, 0, 0], "0.75": [-51.41, 0, 0], "0.8333": [-48.62, 0, 0], "0.9167": [-46.48, 0, 0], "1.0": [-44.84, 0, 0], "1.0833": [-43.5, 0, 0], "1.125": [-42.88, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0, -1, -2], "0.1667": [0, -1, -2], "0.25": [0, -1, -2], "0.3333": [0, -1, -2], "0.4167": [0, -1, -2], "0.5": [0, -1, -2], "0.5833": [0, -1, -2], "0.6667": [0, -1, -2], "0.75": [0, -1, -2], "0.8333": [0, -1, -2], "0.9167": [0, -1, -2], "1.0": [0, -1, -2], "1.0833": [0, -1, -2], "1.125": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-96.99, -16.76, -15.51], "0.0833": [-102.21, -16.76, -15.51], "0.1667": [-110.39, -16.76, -15.51], "0.25": [-117.83, -16.76, -15.51], "0.3333": [-120.82, -16.76, -15.51], "0.375": [-119.49, -16.76, -15.51], "0.4167": [-102.03, -16.76, -15.51], "0.5": [-51.99, -16.76, -15.51], "0.5833": [-46.48, -16.76, -15.51], "0.6667": [-41.99, -16.76, -15.51], "0.75": [-38.41, -16.76, -15.51], "0.8333": [-35.61, -16.76, -15.51], "0.9167": [-33.48, -16.76, -15.51], "1.0": [-31.83, -16.76, -15.51], "1.0833": [-30.49, -16.76, -15.51], "1.125": [-29.87, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0, 0, -2], "0.1667": [0, 0, -2], "0.25": [0, 0, -2], "0.3333": [0, 0, -2], "0.4167": [0, 0, -2], "0.5": [0, 0, -2], "0.5833": [0, 0, -2], "0.6667": [0, 0, -2], "0.75": [0, 0, -2], "0.8333": [0, 0, -2], "0.9167": [0, 0, -2], "1.0": [0, 0, -2], "1.0833": [0, 0, -2], "1.125": [0, 0, -2]}}, "sledge_hammer": {"rotation": {"0.0": [130.94, -82.52, -122.16], "0.0833": [133.17, -82.7, -124.7], "0.1667": [137.32, -83.03, -129.41], "0.25": [139.56, -83.21, -131.95], "0.3333": [136.05, -82.93, -127.96], "0.375": [130.94, -82.52, -122.16], "0.4167": [96.51, -80.22, -82.99], "0.5": [14.6, -73.19, 10], "0.5833": [8.68, -71.58, 16.59], "0.6667": [6.03, -69.73, 19.41], "0.75": [5.83, -67.78, 19.41], "0.8333": [7.27, -65.82, 17.52], "0.9167": [9.55, -63.97, 14.69], "1.0": [11.84, -62.34, 11.87], "1.0833": [13.35, -61.05, 9.99], "1.125": [13.55, -60.57, 9.71]}, "position": {"0.0": [2, -2, 0], "0.0833": [2, -2, 0], "0.1667": [2, -2, 0], "0.25": [2, -2, 0], "0.3333": [2, -2, 0], "0.375": [2, -2, 0], "0.4167": [2, -2, 0], "0.5": [2, -2, 0], "0.5833": [2, -2, 0], "0.6667": [2, -2, 0], "0.75": [2, -2, 0], "0.8333": [2, -2, 0], "0.9167": [2, -2, 0], "1.0": [2, -2, 0], "1.0833": [2, -2, 0], "1.125": [2, -2, 0]}}, "left_ear": {"rotation": {"0.0": [45, 0, -22.5], "0.0833": [46.3, 0, -22.5], "0.1667": [49.17, 0, -22.5], "0.25": [52.03, 0, -22.5], "0.3333": [53.33, 0, -22.5], "0.4167": [51.51, 0, -22.5], "0.5": [45, 0, -22.5], "0.5833": [-39.17, 0, -22.5], "0.625": [-67.5, 0, -22.5], "0.6667": [-67.96, 0, -22.5], "0.75": [-61.7, 0, -22.5], "0.8333": [-48.72, 0, -22.5], "0.9167": [-32.45, 0, -22.5], "1.0": [-16.35, 0, -22.5], "1.0833": [-3.83, 0, -22.5], "1.125": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [45, 0, 22.5], "0.0833": [46.3, 0, 22.5], "0.1667": [49.17, 0, 22.5], "0.25": [52.03, 0, 22.5], "0.3333": [53.33, 0, 22.5], "0.4167": [51.51, 0, 22.5], "0.5": [45, 0, 22.5], "0.5833": [-39.17, 0, 22.5], "0.625": [-67.5, 0, 22.5], "0.6667": [-67.96, 0, 22.5], "0.75": [-61.7, 0, 22.5], "0.8333": [-48.72, 0, 22.5], "0.9167": [-32.45, 0, 22.5], "1.0": [-16.35, 0, 22.5], "1.0833": [-3.83, 0, 22.5], "1.125": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.0833": [0.04, -22.5, 0], "0.1667": [0.12, -22.5, 0], "0.25": [0.18, -22.5, 0], "0.3333": [0.16, -22.5, 0], "0.4167": [0, -22.5, 0], "0.5": [-1.94, -22.5, 0], "0.5417": [-2.5, -22.5, 0], "0.5833": [-2.47, -22.5, 0], "0.6667": [-2.25, -22.5, 0], "0.75": [-1.87, -22.5, 0], "0.8333": [-1.41, -22.5, 0], "0.9167": [-0.91, -22.5, 0], "1.0": [-0.46, -22.5, 0], "1.0833": [-0.11, -22.5, 0], "1.125": [0, -22.5, 0]}, "position": {"0.0": [1, 14, 0], "0.0833": [1, 11.87, 0], "0.1667": [1, 8.74, 0], "0.25": [1, 5.26, 0], "0.3333": [1, 2.13, 0], "0.4167": [1, 0, 0], "0.5": [1, -0.64, 0], "0.5833": [1, -0.96, 0], "0.6667": [1, -1.03, 0], "0.75": [1, -0.92, 0], "0.8333": [1, -0.7, 0], "0.9167": [1, -0.43, 0], "1.0": [1, -0.18, 0], "1.0833": [1, -0.02, 0], "1.125": [1, 0, 0]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [-0.07, 22.38, -0.02], "0.1667": [-0.18, 22.19, -0.05], "0.25": [-0.2, 22.15, -0.05], "0.3333": [0, 22.5, 0], "0.4167": [1.58, 25.29, 0.41], "0.5": [2.81, 27.46, 0.73], "0.5833": [2.7, 27.25, 0.7], "0.6667": [2.39, 26.72, 0.62], "0.75": [1.96, 25.95, 0.51], "0.8333": [1.45, 25.06, 0.38], "0.9167": [0.94, 24.15, 0.24], "1.0": [0.47, 23.33, 0.12], "1.0833": [0.12, 22.71, 0.03], "1.125": [0, 22.5, 0]}, "position": {"0.0": [-1, 12, 0], "0.0833": [-1, 9.56, 0], "0.1667": [-1, 6, 0], "0.25": [-1, 2.44, 0], "0.3333": [-1, 0, 0], "0.4167": [-1, -0.51, 0], "0.5": [-1, -0.79, 0], "0.5833": [-1, -0.89, 0], "0.6667": [-1, -0.85, 0], "0.75": [-1, -0.71, 0], "0.8333": [-1, -0.51, 0], "0.9167": [-1, -0.31, 0], "1.0": [-1, -0.13, 0], "1.0833": [-1, -0.02, 0], "1.125": [-1, 0, 0]}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.0833": [1, 1.0188, 1], "0.1667": [1, 1.05, 1], "0.25": [1, 1.0563, 1], "0.3333": [1, 1, 1], "0.4167": [1, 0.55, 1], "0.5": [1, 0.2, 1], "0.5833": [1, 0.2327, 1], "0.6667": [1, 0.3195, 1], "0.75": [1, 0.4432, 1], "0.8333": [1, 0.5868, 1], "0.9167": [1, 0.7333, 1], "1.0": [1, 0.8656, 1], "1.0833": [1, 0.9666, 1], "1.125": [1, 1, 1]}}, "root": {"position": {"0.0": [0, 0, 0], "0.0833": [0, 10.65, 0], "0.1667": [0, 26.44, 0], "0.25": [0, 40.76, 0], "0.3333": [0, 47, 0], "0.4167": [0, 26.44, 0], "0.5": [0, 0, 0]}, "scale": {"0.0": [0, 0, 0], "0.0833": [0.0344, 0.236, 0.152], "0.1667": [0.0792, 0.588, 0.376], "0.25": [0.1368, 0.972, 0.624], "0.3333": [0.2096, 1.304, 0.848], "0.4167": [0.3, 1.5, 1], "0.5": [0.6688, 1.3438, 1.0625], "0.5833": [1, 1, 1]}}, "head": {"rotation": {"0.0": [22.5, 0, 0], "0.0833": [22.4, 0, 0], "0.1667": [22.18, 0, 0], "0.25": [21.99, 0, 0], "0.3333": [21.96, 0, 0], "0.4167": [22.22, 0, 0], "0.4583": [22.5, 0, 0], "0.5": [25.83, 0, 0], "0.5833": [30, 0, 0], "0.6667": [27.15, 0, 0], "0.75": [22.55, 0, 0], "0.8333": [16.94, 0, 0], "0.9167": [11.06, 0, 0], "1.0": [5.64, 0, 0], "1.0833": [1.43, 0, 0], "1.125": [0, 0, 0]}, "position": [0, 0, 0]}}}, "animation.ptd_bb.piglin_maruarder.idle": {"loop": true, "animation_length": 2, "bones": {"torso": {"rotation": {"0.0": [6, 0, 0], "0.0833": [5.97, 0, 0], "0.1667": [5.87, 0, 0], "0.25": [5.71, 0, 0], "0.3333": [5.5, 0, 0], "0.4167": [5.26, 0, 0], "0.5": [5, 0, 0], "0.5833": [4.74, 0, 0], "0.6667": [4.5, 0, 0], "0.75": [4.29, 0, 0], "0.8333": [4.13, 0, 0], "0.9167": [4.03, 0, 0], "1.0": [4, 0, 0], "1.0833": [4.03, 0, 0], "1.1667": [4.13, 0, 0], "1.25": [4.29, 0, 0], "1.3333": [4.5, 0, 0], "1.4167": [4.74, 0, 0], "1.5": [5, 0, 0], "1.5833": [5.26, 0, 0], "1.6667": [5.5, 0, 0], "1.75": [5.71, 0, 0], "1.8333": [5.87, 0, 0], "1.9167": [5.97, 0, 0], "2.0": [6, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.0833": [-42.24, 0, 0], "0.1667": [-42, 0, 0], "0.25": [-41.79, 0, 0], "0.3333": [-41.63, 0, 0], "0.4167": [-41.53, 0, 0], "0.5": [-41.5, 0, 0], "0.5833": [-41.53, 0, 0], "0.6667": [-41.63, 0, 0], "0.75": [-41.79, 0, 0], "0.8333": [-42, 0, 0], "0.9167": [-42.24, 0, 0], "1.0": [-42.5, 0, 0], "1.0833": [-42.76, 0, 0], "1.1667": [-43, 0, 0], "1.25": [-43.21, 0, 0], "1.3333": [-43.37, 0, 0], "1.4167": [-43.47, 0, 0], "1.5": [-43.5, 0, 0], "1.5833": [-43.47, 0, 0], "1.6667": [-43.37, 0, 0], "1.75": [-43.21, 0, 0], "1.8333": [-43, 0, 0], "1.9167": [-42.76, 0, 0], "2.0": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0, -1, -2], "0.1667": [0, -1, -2], "0.25": [0, -1, -2], "0.3333": [0, -1, -2], "0.4167": [0, -1, -2], "0.5": [0, -1, -2], "0.5833": [0, -1, -2], "0.6667": [0, -1, -2], "0.75": [0, -1, -2], "0.8333": [0, -1, -2], "0.9167": [0, -1, -2], "1.0": [0, -1, -2], "1.0833": [0, -1, -2], "1.1667": [0, -1, -2], "1.25": [0, -1, -2], "1.3333": [0, -1, -2], "1.4167": [0, -1, -2], "1.5": [0, -1, -2], "1.5833": [0, -1, -2], "1.6667": [0, -1, -2], "1.75": [0, -1, -2], "1.8333": [0, -1, -2], "1.9167": [0, -1, -2], "2.0": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.49, -16.76, -15.51], "0.0833": [-29.23, -16.76, -15.51], "0.1667": [-28.99, -16.76, -15.51], "0.25": [-28.78, -16.76, -15.51], "0.3333": [-28.63, -16.76, -15.51], "0.4167": [-28.53, -16.76, -15.51], "0.5": [-28.49, -16.76, -15.51], "0.5833": [-28.53, -16.76, -15.51], "0.6667": [-28.63, -16.76, -15.51], "0.75": [-28.78, -16.76, -15.51], "0.8333": [-28.99, -16.76, -15.51], "0.9167": [-29.23, -16.76, -15.51], "1.0": [-29.49, -16.76, -15.51], "1.0833": [-29.75, -16.76, -15.51], "1.1667": [-29.99, -16.76, -15.51], "1.25": [-30.2, -16.76, -15.51], "1.3333": [-30.36, -16.76, -15.51], "1.4167": [-30.46, -16.76, -15.51], "1.5": [-30.49, -16.76, -15.51], "1.5833": [-30.46, -16.76, -15.51], "1.6667": [-30.36, -16.76, -15.51], "1.75": [-30.2, -16.76, -15.51], "1.8333": [-29.99, -16.76, -15.51], "1.9167": [-29.75, -16.76, -15.51], "2.0": [-29.49, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0, 0, -2], "0.1667": [0, 0, -2], "0.25": [0, 0, -2], "0.3333": [0, 0, -2], "0.4167": [0, 0, -2], "0.5": [0, 0, -2], "0.5833": [0, 0, -2], "0.6667": [0, 0, -2], "0.75": [0, 0, -2], "0.8333": [0, 0, -2], "0.9167": [0, 0, -2], "1.0": [0, 0, -2], "1.0833": [0, 0, -2], "1.1667": [0, 0, -2], "1.25": [0, 0, -2], "1.3333": [0, 0, -2], "1.4167": [0, 0, -2], "1.5": [0, 0, -2], "1.5833": [0, 0, -2], "1.6667": [0, 0, -2], "1.75": [0, 0, -2], "1.8333": [0, 0, -2], "1.9167": [0, 0, -2], "2.0": [0, 0, -2]}}, "sledge_hammer": {"rotation": {"0.0": [13.55, -60.57, 9.71], "0.0833": [13.55, -60.57, 9.71], "0.1667": [13.55, -60.57, 9.71], "0.25": [13.55, -60.57, 9.71], "0.3333": [13.55, -60.57, 9.71], "0.4167": [13.55, -60.57, 9.71], "0.5": [13.55, -60.57, 9.71], "0.5833": [13.55, -60.57, 9.71], "0.6667": [13.55, -60.57, 9.71], "0.75": [13.55, -60.57, 9.71], "0.8333": [13.55, -60.57, 9.71], "0.9167": [13.55, -60.57, 9.71], "1.0": [13.55, -60.57, 9.71], "1.0833": [13.55, -60.57, 9.71], "1.1667": [13.55, -60.57, 9.71], "1.25": [13.55, -60.57, 9.71], "1.3333": [13.55, -60.57, 9.71], "1.4167": [13.55, -60.57, 9.71], "1.5": [13.55, -60.57, 9.71], "1.5833": [13.55, -60.57, 9.71], "1.6667": [13.55, -60.57, 9.71], "1.75": [13.55, -60.57, 9.71], "1.8333": [13.55, -60.57, 9.71], "1.9167": [13.55, -60.57, 9.71], "2.0": [13.55, -60.57, 9.71]}, "position": {"0.0": [2, -2, 0], "0.0833": [2, -2, 0], "0.1667": [2, -2, 0], "0.25": [2, -2, 0], "0.3333": [2, -2, 0], "0.4167": [2, -2, 0], "0.5": [2, -2, 0], "0.5833": [2, -2, 0], "0.6667": [2, -2, 0], "0.75": [2, -2, 0], "0.8333": [2, -2, 0], "0.9167": [2, -2, 0], "1.0": [2, -2, 0], "1.0833": [2, -2, 0], "1.1667": [2, -2, 0], "1.25": [2, -2, 0], "1.3333": [2, -2, 0], "1.4167": [2, -2, 0], "1.5": [2, -2, 0], "1.5833": [2, -2, 0], "1.6667": [2, -2, 0], "1.75": [2, -2, 0], "1.8333": [2, -2, 0], "1.9167": [2, -2, 0], "2.0": [2, -2, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -18.5], "0.0833": [0, 0, -18.64], "0.1667": [0, 0, -19.04], "0.25": [0, 0, -19.67], "0.3333": [0, 0, -20.5], "0.4167": [0, 0, -21.46], "0.5": [0, 0, -22.5], "0.5833": [0, 0, -23.54], "0.6667": [0, 0, -24.5], "0.75": [0, 0, -25.33], "0.8333": [0, 0, -25.96], "0.9167": [0, 0, -26.36], "1.0": [0, 0, -26.5], "1.0833": [0, 0, -26.36], "1.1667": [0, 0, -25.96], "1.25": [0, 0, -25.33], "1.3333": [0, 0, -24.5], "1.4167": [0, 0, -23.54], "1.5": [0, 0, -22.5], "1.5833": [0, 0, -21.46], "1.6667": [0, 0, -20.5], "1.75": [0, 0, -19.67], "1.8333": [0, 0, -19.04], "1.9167": [0, 0, -18.64], "2.0": [0, 0, -18.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 18.5], "0.0833": [0, 0, 18.64], "0.1667": [0, 0, 19.04], "0.25": [0, 0, 19.67], "0.3333": [0, 0, 20.5], "0.4167": [0, 0, 21.46], "0.5": [0, 0, 22.5], "0.5833": [0, 0, 23.54], "0.6667": [0, 0, 24.5], "0.75": [0, 0, 25.33], "0.8333": [0, 0, 25.96], "0.9167": [0, 0, 26.36], "1.0": [0, 0, 26.5], "1.0833": [0, 0, 26.36], "1.1667": [0, 0, 25.96], "1.25": [0, 0, 25.33], "1.3333": [0, 0, 24.5], "1.4167": [0, 0, 23.54], "1.5": [0, 0, 22.5], "1.5833": [0, 0, 21.46], "1.6667": [0, 0, 20.5], "1.75": [0, 0, 19.67], "1.8333": [0, 0, 19.04], "1.9167": [0, 0, 18.64], "2.0": [0, 0, 18.5]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.0833": [0, -22.5, 0], "0.1667": [0, -22.5, 0], "0.25": [0, -22.5, 0], "0.3333": [0, -22.5, 0], "0.4167": [0, -22.5, 0], "0.5": [0, -22.5, 0], "0.5833": [0, -22.5, 0], "0.6667": [0, -22.5, 0], "0.75": [0, -22.5, 0], "0.8333": [0, -22.5, 0], "0.9167": [0, -22.5, 0], "1.0": [0, -22.5, 0], "1.0833": [0, -22.5, 0], "1.1667": [0, -22.5, 0], "1.25": [0, -22.5, 0], "1.3333": [0, -22.5, 0], "1.4167": [0, -22.5, 0], "1.5": [0, -22.5, 0], "1.5833": [0, -22.5, 0], "1.6667": [0, -22.5, 0], "1.75": [0, -22.5, 0], "1.8333": [0, -22.5, 0], "1.9167": [0, -22.5, 0], "2.0": [0, -22.5, 0]}, "position": {"0.0": [1, 0, 0], "0.0833": [1, 0, 0], "0.1667": [1, 0, 0], "0.25": [1, 0, 0], "0.3333": [1, 0, 0], "0.4167": [1, 0, 0], "0.5": [1, 0, 0], "0.5833": [1, 0, 0], "0.6667": [1, 0, 0], "0.75": [1, 0, 0], "0.8333": [1, 0, 0], "0.9167": [1, 0, 0], "1.0": [1, 0, 0], "1.0833": [1, 0, 0], "1.1667": [1, 0, 0], "1.25": [1, 0, 0], "1.3333": [1, 0, 0], "1.4167": [1, 0, 0], "1.5": [1, 0, 0], "1.5833": [1, 0, 0], "1.6667": [1, 0, 0], "1.75": [1, 0, 0], "1.8333": [1, 0, 0], "1.9167": [1, 0, 0], "2.0": [1, 0, 0]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 22.5, 0], "0.1667": [0, 22.5, 0], "0.25": [0, 22.5, 0], "0.3333": [0, 22.5, 0], "0.4167": [0, 22.5, 0], "0.5": [0, 22.5, 0], "0.5833": [0, 22.5, 0], "0.6667": [0, 22.5, 0], "0.75": [0, 22.5, 0], "0.8333": [0, 22.5, 0], "0.9167": [0, 22.5, 0], "1.0": [0, 22.5, 0], "1.0833": [0, 22.5, 0], "1.1667": [0, 22.5, 0], "1.25": [0, 22.5, 0], "1.3333": [0, 22.5, 0], "1.4167": [0, 22.5, 0], "1.5": [0, 22.5, 0], "1.5833": [0, 22.5, 0], "1.6667": [0, 22.5, 0], "1.75": [0, 22.5, 0], "1.8333": [0, 22.5, 0], "1.9167": [0, 22.5, 0], "2.0": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, 0], "0.0833": [-1, 0, 0], "0.1667": [-1, 0, 0], "0.25": [-1, 0, 0], "0.3333": [-1, 0, 0], "0.4167": [-1, 0, 0], "0.5": [-1, 0, 0], "0.5833": [-1, 0, 0], "0.6667": [-1, 0, 0], "0.75": [-1, 0, 0], "0.8333": [-1, 0, 0], "0.9167": [-1, 0, 0], "1.0": [-1, 0, 0], "1.0833": [-1, 0, 0], "1.1667": [-1, 0, 0], "1.25": [-1, 0, 0], "1.3333": [-1, 0, 0], "1.4167": [-1, 0, 0], "1.5": [-1, 0, 0], "1.5833": [-1, 0, 0], "1.6667": [-1, 0, 0], "1.75": [-1, 0, 0], "1.8333": [-1, 0, 0], "1.9167": [-1, 0, 0], "2.0": [-1, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.idle_2": {"loop": "hold_on_last_frame", "animation_length": 9.9583, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.1667": [6.57, 0, 0], "0.25": [7.5, 0, 0], "0.3333": [8.8, 0, 0], "0.5": [10.69, 0, 0], "0.5417": [10, 0, 0], "0.6667": [-3.54, 0, 0], "0.7083": [-7.5, 0, 0], "0.8333": [-10.52, 0, 0], "1.0": [-12.5, 0, 0], "1.1667": [-17.5, 0, 0], "1.25": [-17.5, 0, 0], "1.3333": [-12.96, 0, 0], "1.5": [0, 0, 0], "1.6667": [5.86, -1.28, -0.17], "1.75": [7.5, 0, 0], "1.8333": [8.12, 2.27, 0.31], "2.0": [8.66, 9.32, 1.26], "2.1667": [8.67, 16.17, 2.19], "2.2083": [8.65, 17.35, 2.36], "2.3333": [8.43, 18.81, 2.56], "2.5": [7.97, 20.19, 2.76], "2.6667": [7.49, 21.1, 2.9], "2.8333": [7.18, 21.7, 3], "3.0": [7.1, 22.18, 3.08], "3.0417": [7.12, 22.3, 3.09], "3.1667": [7.26, 22.65, 3.15], "3.3333": [7.62, 22.51, 3.13], "3.5": [8.11, 22.3, 3.09], "3.6667": [8.61, 23.86, 3.31], "3.8333": [8.99, 25.42, 3.53], "4.0": [9.11, 22.3, 3.09], "4.1667": [8.93, 10, 1.4], "4.3333": [8.49, -6.98, -0.94], "4.5": [7.98, -19.82, -2.72], "4.6667": [7.49, -22.03, -3.03], "4.8333": [7.15, -23.52, -3.24], "5.0": [7.05, -24.41, -3.38], "5.1667": [7.22, -24.84, -3.45], "5.3333": [7.63, -24.95, -3.47], "5.5": [8.17, -24.86, -3.47], "5.6667": [8.71, -24.72, -3.46], "5.8333": [9.11, -24.64, -3.46], "6.0": [9.27, -24.77, -3.48], "6.1667": [9.19, -25.34, -3.57], "6.3333": [8.91, -26.06, -3.67], "6.5": [8.49, -26.63, -3.75], "6.6667": [8.02, -26.79, -3.77], "6.8333": [7.59, -26.26, -3.69], "7.0": [7.27, -24.77, -3.48], "7.1667": [7, -21.79, -3.06], "7.3333": [6.78, -17.43, -2.45], "7.5": [6.63, -12.39, -1.74], "7.6667": [6.5, -7.34, -1.03], "7.8333": [6.31, -2.98, -0.42], "8.0": [6, 0, 0], "8.1667": [5, 0, 0], "8.3333": [6, 0, 0], "8.4167": [7.5, 0, 0], "8.5": [10.83, 0, 0], "8.6667": [17.84, 0, 0], "8.7083": [17.5, 0, 0], "8.8333": [-2.19, 0, 0], "8.875": [-7.5, 0, 0], "9.0": [-9.6, 0, 0], "9.0833": [-7.5, 0, 0], "9.1667": [-4.07, 0, 0], "9.3333": [5, 0, 0], "9.4167": [12.5, 0, 0], "9.5": [15.52, 0, 0], "9.625": [17.5, 0, 0], "9.6667": [16.52, 0, 0], "9.8333": [8.17, 0, 0], "9.9167": [5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, -1.33, 0.7], "0.25": [0, -2, 1], "0.3333": [0, -2.48, 1.1], "0.5": [0, -3.08, 1.06], "0.5417": [0, -3, 1], "0.6667": [0, -0.63, 0.2], "0.7083": [0, 0, 0], "0.8333": [0, 0.31, -0.07], "1.0": [0, 0, 0], "1.125": [0, -2, 0], "1.1667": [0, -1.56, 0], "1.25": [0, 0, 0], "1.3333": [0, 0.01, 0], "1.5": [0, 0.03, 0], "1.6667": [0, 0.05, 0], "1.8333": [0, 0.07, 0], "2.0": [0, 0.09, 0], "2.1667": [0, 0.1, 0], "2.3333": [0, 0.11, 0], "2.5": [0, 0.12, 0], "2.6667": [0, 0.13, 0], "2.8333": [0, 0.14, 0], "3.0": [0, 0.14, 0], "3.1667": [0, 0.15, 0], "3.3333": [0, 0.15, 0], "3.5": [0, 0.15, 0], "3.6667": [0, 0.15, 0], "3.8333": [0, 0.15, 0], "4.0": [0, 0.14, 0], "4.1667": [0, 0.14, 0], "4.3333": [0, 0.13, 0], "4.5": [0, 0.13, 0], "4.6667": [0, 0.12, 0], "4.8333": [0, 0.12, 0], "5.0": [0, 0.11, 0], "5.1667": [0, 0.1, 0], "5.3333": [0, 0.09, 0], "5.5": [0, 0.09, 0], "5.6667": [0, 0.08, 0], "5.8333": [0, 0.07, 0], "6.0": [0, 0.06, 0], "6.1667": [0, 0.05, 0], "6.3333": [0, 0.05, 0], "6.5": [0, 0.04, 0], "6.6667": [0, 0.03, 0], "6.8333": [0, 0.02, 0], "7.0": [0, 0.02, 0], "7.1667": [0, 0.01, 0], "7.3333": [0, 0.01, 0], "7.5": [0, 0.01, 0], "7.6667": [0, 0, 0], "7.8333": [0, 0, 0], "8.0": [0, 0, 0], "8.1667": [0, 0, 0], "8.3333": [0, -1.33, 0.7], "8.4167": [0, -2, 1], "8.5": [0, -2.48, 1.1], "8.6667": [0, -3.08, 1.06], "8.7083": [0, -3, 1], "8.8333": [0, -0.63, 0.2], "8.875": [0, 0, 0], "9.0": [0, 0.22, -0.05], "9.0833": [0, 0, 0], "9.1667": [0, -0.3, 0], "9.3333": [0, -1, 0], "9.4583": [0, -1, 0], "9.5": [0, -0.8, 0], "9.625": [0, 0, 0], "9.6667": [0, 0.05, 0], "9.8333": [0, 0.03, 0], "9.9167": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-35, 0, 0], "lerp_mode": "catmullrom"}, "0.7083": {"post": [-102.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [-83.07483, -31.60176, -33.60019], "lerp_mode": "catmullrom"}, "1.375": {"post": [-4.83045, -1.29256, -14.94548], "lerp_mode": "catmullrom"}, "1.75": {"post": [0.16955, -1.29256, -14.94548], "lerp_mode": "catmullrom"}, "2.2083": {"post": [7.75236, -0.62127, -19.90093], "lerp_mode": "catmullrom"}, "2.75": {"post": [-7.2677, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "3.5417": {"post": [-7.2677, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "3.9167": {"post": [-7.2677, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "4.375": {"post": [-18.04514, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "5.0": {"post": [0.2323, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "6.0": {"post": [2.7323, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "8.2917": {"post": [2.7323, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "8.5417": {"post": [25.2323, -0.30539, -17.42088], "lerp_mode": "catmullrom"}, "8.7917": {"post": [30.1604, 2.21063, -21.74267], "lerp_mode": "catmullrom"}, "9.125": {"post": [-19.80224, 0.53005, -34.90112], "lerp_mode": "catmullrom"}, "9.375": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "1.375": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "8.2917": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "8.5417": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "8.7917": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "9.125": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "9.375": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-29.4919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-21.9919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}, "0.875": {"post": [-92.7306, -13.26763, -51.45651], "lerp_mode": "catmullrom"}, "1.125": {"post": [-112.01931, -19.7091, -58.15339], "lerp_mode": "catmullrom"}, "8.25": {"post": [-112.01931, -19.7091, -58.15339], "lerp_mode": "catmullrom"}, "8.5417": {"post": [-114.37284, -31.2081, -52.70916], "lerp_mode": "catmullrom"}, "8.7917": {"post": [-114.37284, -31.2081, -52.70916], "lerp_mode": "catmullrom"}, "9.0": {"post": [-111.02983, -10.40298, -61.94869], "lerp_mode": "catmullrom"}, "9.25": {"post": [-111.02983, -10.40298, -61.94869], "lerp_mode": "catmullrom"}, "9.4583": {"post": [-29.4919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "1.125": {"post": [-1, 0, 1], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "8.25": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "8.5417": {"post": [0, 0.44, -1], "lerp_mode": "catmullrom"}, "8.7917": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "9.0": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "9.25": {"post": [0, 1.44, -1], "lerp_mode": "catmullrom"}, "9.4583": {"post": [0, 0, -2], "lerp_mode": "catmullrom"}}}, "sledge_hammer": {"rotation": {"0.0": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}, "0.75": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}, "1.0833": {"post": [-82.2111, -63.08598, 24.72056], "lerp_mode": "catmullrom"}, "8.7917": {"post": [-82.2111, -63.08598, 24.72056], "lerp_mode": "catmullrom"}, "8.9583": {"post": [-37.2111, -63.08598, 24.72056], "lerp_mode": "catmullrom"}, "9.25": {"post": [-10.95386, -81.24623, 21.62197], "lerp_mode": "catmullrom"}, "9.4583": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}, "1.0833": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "8.7917": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "9.4583": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "0.875": {"post": [-20.94102, -40.78947, -82.13881], "lerp_mode": "catmullrom"}, "1.125": {"post": [-20.41789, -15.45579, -78.05668], "lerp_mode": "catmullrom"}, "1.2083": {"post": [16.88274, 2.12203, -10.53164], "lerp_mode": "catmullrom"}, "1.3333": {"post": [14.50413, 8.98691, -34.70068], "lerp_mode": "catmullrom"}, "1.5": {"post": [5.82232, -24.86747, -43.81078], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "8.2917": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "8.8333": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "9.1667": {"post": [-20.94102, -40.78947, -82.13881], "lerp_mode": "catmullrom"}, "9.4167": {"post": [-20.41789, -15.45579, -78.05668], "lerp_mode": "catmullrom"}, "9.5": {"post": [16.88274, 2.12203, -10.53164], "lerp_mode": "catmullrom"}, "9.625": {"post": [14.50413, 8.98691, -34.70068], "lerp_mode": "catmullrom"}, "9.7917": {"post": [5.82232, -24.86747, -43.81078], "lerp_mode": "catmullrom"}, "9.9583": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "0.875": {"post": [-20.94102, 40.78947, 82.13881], "lerp_mode": "catmullrom"}, "1.125": {"post": [-20.41789, 15.45579, 78.05668], "lerp_mode": "catmullrom"}, "1.2083": {"post": [16.88274, -2.12203, 10.53164], "lerp_mode": "catmullrom"}, "1.3333": {"post": [14.50413, -8.98691, 34.70068], "lerp_mode": "catmullrom"}, "1.5": {"post": [5.82232, 24.86747, 43.81078], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "8.2917": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "8.8333": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "9.1667": {"post": [-20.94102, 40.78947, 82.13881], "lerp_mode": "catmullrom"}, "9.4167": {"post": [-20.41789, 15.45579, 78.05668], "lerp_mode": "catmullrom"}, "9.5": {"post": [16.88274, -2.12203, 10.53164], "lerp_mode": "catmullrom"}, "9.625": {"post": [14.50413, -8.98691, 34.70068], "lerp_mode": "catmullrom"}, "9.7917": {"post": [5.82232, 24.86747, 43.81078], "lerp_mode": "catmullrom"}, "9.9583": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [5, -22.5, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [7.80612, -22.5, 0], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [5, -22.5, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "8.1667": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "8.4583": {"post": [5, -22.5, 0], "lerp_mode": "catmullrom"}, "8.7083": {"post": [7.80612, -22.5, 0], "lerp_mode": "catmullrom"}, "8.875": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "9.25": {"post": [5, -22.5, 0], "lerp_mode": "catmullrom"}, "9.625": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "0.7083": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "1.0833": {"post": [1, -1, 0], "lerp_mode": "catmullrom"}, "1.1667": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "8.1667": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "8.875": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "9.25": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "9.3333": {"post": [1, -1, 0], "lerp_mode": "catmullrom"}, "9.4167": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "9.625": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [5, 22.5, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [7.80612, 22.5, 0], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "0.875": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "8.25": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "8.5417": {"post": [5, 22.5, 0], "lerp_mode": "catmullrom"}, "8.7917": {"post": [7.80612, 22.5, 0], "lerp_mode": "catmullrom"}, "8.9583": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "9.125": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "9.25": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "9.625": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "0.7083": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "0.875": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "1.0833": {"post": [-1, -1, 0], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "8.25": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "8.9583": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "9.125": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "9.25": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "9.3333": {"post": [-1, -1, 0], "lerp_mode": "catmullrom"}, "9.4167": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "9.625": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.2917": [1, 0.2, 1], "0.625": [1, 0.2, 1], "0.875": [1, 1.3, 1], "1.125": [1, 1, 1], "1.2083": [1, 0.2, 1], "1.5": [1, 1, 1], "2.5": [1, 0.3, 1], "3.75": [1, 0.3, 1], "4.0": [1, 1, 1], "4.375": [1, 1, 1], "4.8333": [1, 0.3, 1], "7.9583": [1, 1, 1], "8.2917": [1, 1, 1], "8.5833": [1, 0.2, 1], "8.9167": [1, 0.2, 1], "9.1667": [1, 1.3, 1], "9.4167": [1, 1, 1], "9.5": [1, 0.2, 1], "9.7917": [1, 1, 1]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [5.71, 0, 0], "0.2917": [10, 0, 0], "0.3333": [10.88, 0, 0], "0.5": [12.31, 0, 0], "0.5417": [12.5, 0, 0], "0.6667": [14.14, 0, 0], "0.7083": [12.5, 0, 0], "0.8333": [-5.43, 0, 0], "0.875": [-10, 0, 0], "1.0": [-11.8, 0, 0], "1.0833": [-10, 0, 0], "1.1667": [0, 0, 0], "1.3333": [0.63, -0.94, 0], "1.5": [0, 0, 0], "1.6667": [0, 4.44, 0], "1.8333": [0, 10.56, 0], "2.0": [0, 15, 0], "2.1667": [0, 15.86, 0], "2.3333": [0, 16.73, 0], "2.5": [0, 17.5, 0], "2.6667": [0, 18.09, 0], "2.8333": [0, 18.4, 0], "3.0": [0, 18.33, 0], "3.1667": [0, 17.81, 0], "3.3333": [0, 16.73, 0], "3.5": [0, 15, 0], "3.6667": [0, 10.49, 0], "3.8333": [0, 3.89, 0], "4.0": [0, -3.75, 0], "4.1667": [0, -11.39, 0], "4.3333": [0, -17.99, 0], "4.5": [0, -22.5, 0], "4.6667": [0, -23.63, 0], "4.8333": [0, -24.53, 0], "5.0": [0, -25.22, 0], "5.1667": [0, -25.71, 0], "5.3333": [0, -26.02, 0], "5.5": [0, -26.16, 0], "5.6667": [0, -26.15, 0], "5.8333": [0, -26.01, 0], "6.0": [0, -25.74, 0], "6.1667": [0, -25.37, 0], "6.3333": [0, -24.91, 0], "6.5": [0, -24.38, 0], "6.6667": [0, -23.79, 0], "6.8333": [0, -23.16, 0], "7.0": [0, -22.5, 0], "7.1667": [0, -17.25, 0], "7.3333": [0, -9.83, 0], "7.5": [0, -3.52, 0], "7.5417": [0, -2.5, 0], "7.6667": [0, -0.39, 0], "7.8333": [0, -0.02, 0], "7.9583": [0, 0, 0], "8.0": [-0.1, 0.12, 0], "8.1667": [-1.1, 0.11, 0], "8.2917": [0, 0, 0], "8.3333": [1.26, 0, 0], "8.5": [9.81, 0, 0], "8.625": [15, 0, 0], "8.6667": [16.48, 0, 0], "8.8333": [15, 0, 0], "9.0": [-5.37, 0, 0], "9.125": [-17.5, 0, 0], "9.1667": [-17.45, 0, 0], "9.3333": [-3.35, 0, 0], "9.375": [0, 0, 0], "9.5": [15, 0, 0], "9.6667": [5, 0, 0], "9.75": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.2917": [0, 0, 0], "0.3333": [0, 0, 0], "0.5": [0, 0, 0], "0.6667": [0, 0, 0], "0.8333": [0, 0, 0], "1.0": [0, 0, 0], "1.1667": [0, 0, 0], "1.3333": [0, 0, 0], "1.5": [0, 0, 0], "1.6667": [0, 0, 0], "1.8333": [0, 0, 0], "2.0": [0, 0, 0], "2.1667": [0, 0, 0], "2.3333": [0, 0, 0], "2.5": [0, 0, 0], "2.6667": [0, 0, 0], "2.8333": [0, 0, 0], "3.0": [0, 0, 0], "3.1667": [0, 0, 0], "3.3333": [0, 0, 0], "3.5": [0, 0, 0], "3.6667": [0, 0, 0], "3.8333": [0, 0, 0], "4.0": [0, 0, 0], "4.1667": [0, 0, 0], "4.3333": [0, 0, 0], "4.5": [0, 0, 0], "4.6667": [0, 0, 0], "4.8333": [0, 0, 0], "5.0": [0, 0, 0], "5.1667": [0, 0, 0], "5.3333": [0, 0, 0], "5.5": [0, 0, 0], "5.6667": [0, 0, 0], "5.8333": [0, 0, 0], "6.0": [0, 0, 0], "6.1667": [0, 0, 0], "6.3333": [0, 0, 0], "6.5": [0, 0, 0], "6.6667": [0, 0, 0], "6.8333": [0, 0, 0], "7.0": [0, 0, 0], "7.1667": [0, 0, 0], "7.3333": [0, 0, 0], "7.5": [0, 0, 0], "7.6667": [0, 0, 0], "7.8333": [0, 0, 0], "7.9583": [0, 0, 0], "8.0": [0, 0, 0], "8.1667": [0, 0, 0], "8.2917": [0, 0, 0], "8.3333": [0, 0, 0], "8.5": [0, 0, 0], "8.6667": [0, 0, 0], "8.8333": [0, 0, 0], "9.0": [0, 0, 0], "9.1667": [0, 0, 0], "9.3333": [0, 0, 0], "9.375": [0, 0, 0], "9.5": [0, 0, 0], "9.6667": [0, 0, 0], "9.75": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.idle_3": {"loop": true, "animation_length": 8, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.25": [5.65, 0, 0], "0.5": [5.42, 0, 0], "0.75": [4.5, 0, 0], "0.9167": [4.03, 0, 0], "1.0": [3.97, 0, 0], "1.25": [4.12, 0, 0], "1.5": [4.68, 0, 0], "1.75": [5.26, 0, 0], "2.0": [5.48, 0, 0], "2.25": [5.29, 0, 0], "2.5": [5, 0, 0], "2.75": [8.82, 0, 0], "3.0": [11.5, 0, 0], "3.25": [5.23, 0, 0], "3.5": [0, 0, 0], "3.75": [6.96, 0, 0], "4.0": [13.5, 0, 0], "4.25": [6.96, 0, 0], "4.5": [0, 0, 0], "4.75": [5.54, 0, 0], "5.0": [11.5, 0, 0], "5.25": [5.54, 0, 0], "5.5": [0, 0, 0], "5.75": [6.96, 0, 0], "6.0": [13.5, 0, 0], "6.25": [7.89, 0, 0], "6.5": [0, 0, 0], "6.75": [-3.25, 0, 0], "7.0": [-3.5, 0, 0], "7.25": [-1.7, 0, 0], "7.5": [1.09, 0, 0], "7.75": [3.51, 0, 0], "8.0": [5, 0, 0]}}, "left_arm": {"rotation": {"0.0": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "3.0": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}, "3.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "4.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "5.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "6.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "6.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-29.4919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-46.9919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}, "2.0": {"post": [-48.94253, -21.78493, -9.60726], "lerp_mode": "catmullrom"}, "3.0": {"post": [-42.5, 0, 0], "lerp_mode": "catmullrom"}, "3.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "4.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "5.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "6.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "6.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [-29.4919, -16.75858, -15.50706], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, -2], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, -2], "lerp_mode": "catmullrom"}}}, "sledge_hammer": {"rotation": {"0.0": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}, "2.0": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}, "3.0": {"post": [-96.1466, -81.84543, 96.53674], "lerp_mode": "catmullrom"}, "6.5": {"post": [-96.1466, -81.84543, 96.53674], "lerp_mode": "catmullrom"}, "8.0": {"post": [13.54545, -60.57268, 9.70542], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}, "3.0": {"post": [2, -2, -3], "lerp_mode": "catmullrom"}, "6.5": {"post": [2, -2, -3], "lerp_mode": "catmullrom"}, "8.0": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [-22.32648, 2.86313, -15.56512], "lerp_mode": "catmullrom"}, "3.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "3.5": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "4.5": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "5.5": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "6.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "6.5": {"post": [-22.5, 0, -22.5], "lerp_mode": "catmullrom"}, "7.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, -22.5], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [-22.42287, 1.91134, 27.12111], "lerp_mode": "catmullrom"}, "3.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "3.5": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "4.5": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "5.5": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "6.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "6.5": {"post": [-22.5, 0, 22.5], "lerp_mode": "catmullrom"}, "7.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 22.5], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, -22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [1, 0, 0], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [-1, 0, 0], "lerp_mode": "catmullrom"}}}, "eyes": {"scale": {"0.0": [1, 1, 1], "2.0": [1, 0.4, 1], "3.0": [1, 1, 1], "3.5": [1, 0.3, 1], "4.0": [1, 1, 1], "4.5": [1, 0.3, 1], "5.0": [1, 1, 1], "5.5": [1, 0.3, 1], "6.0": [1, 1, 1], "6.5": [1, 0.3, 1], "7.0": [1, 1, 1], "8.0": [1, 1, 1]}}, "head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [31.18939, -15.09492, -8.95891], "lerp_mode": "catmullrom"}, "2.5": {"post": [31.5667, -17.2294, -10.3141], "lerp_mode": "catmullrom"}, "3.0": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "3.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "4.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "5.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "6.0": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "6.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "7.0": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.ptd_bb.piglin_maruarder.walk": {"loop": true, "animation_length": 2, "anim_time_update": "q.anim_time + (q.delta_time * q.modified_move_speed * 7)", "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.0833": [9, 0, 0], "0.125": [10, 0, 0], "0.1667": [9.87, 0, 0], "0.25": [9, 0, 0], "0.3333": [7.64, 0, 0], "0.4167": [6.18, 0, 0], "0.5": [5, 0, 0], "0.5833": [3.79, 0, 0], "0.6667": [2.69, 0, 0], "0.75": [1.99, 0, 0], "0.7917": [1.89, 0, 0], "0.8333": [2.08, 0, 0], "0.9167": [3.32, 0, 0], "1.0": [5, 0, 0], "1.0833": [9, 0, 0], "1.125": [10, 0, 0], "1.1667": [9.87, 0, 0], "1.25": [9, 0, 0], "1.3333": [7.64, 0, 0], "1.4167": [6.18, 0, 0], "1.5": [5, 0, 0], "1.5833": [3.79, 0, 0], "1.6667": [2.69, 0, 0], "1.75": [1.99, 0, 0], "1.7917": [1.89, 0, 0], "1.8333": [2.08, 0, 0], "1.9167": [3.32, 0, 0], "2.0": [5, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.0833": [-42.5, 0, 0], "0.125": [-42.5, 0, 0], "0.1667": [-40, 0, 0], "0.2083": [-37.5, 0, 0], "0.25": [-38.06, 0, 0], "0.3333": [-39.17, 0, 0], "0.4167": [-40.28, 0, 0], "0.5": [-41.39, 0, 0], "0.5833": [-42.5, 0, 0], "0.6667": [-42.5, 0, 0], "0.75": [-42.5, 0, 0], "0.8333": [-42.5, 0, 0], "0.9167": [-42.5, 0, 0], "1.0": [-42.5, 0, 0], "1.0833": [-42.5, 0, 0], "1.125": [-42.5, 0, 0], "1.1667": [-40, 0, 0], "1.2083": [-37.5, 0, 0], "1.25": [-38.33, 0, 0], "1.3333": [-40, 0, 0], "1.4167": [-41.67, 0, 0], "1.4583": [-42.5, 0, 0], "1.5": [-42.5, 0, 0], "1.5833": [-42.5, 0, 0], "1.6667": [-42.5, 0, 0], "1.75": [-42.5, 0, 0], "1.8333": [-42.5, 0, 0], "1.9167": [-42.5, 0, 0], "2.0": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0, -1, -2], "0.125": [0, -1, -2], "0.1667": [0, -1, -2], "0.25": [0, -1, -2], "0.3333": [0, -1, -2], "0.4167": [0, -1, -2], "0.5": [0, -1, -2], "0.5833": [0, -1, -2], "0.6667": [0, -1, -2], "0.75": [0, -1, -2], "0.8333": [0, -1, -2], "0.9167": [0, -1, -2], "1.0": [0, -1, -2], "1.0833": [0, -1, -2], "1.125": [0, -1, -2], "1.1667": [0, -1, -2], "1.25": [0, -1, -2], "1.3333": [0, -1, -2], "1.4167": [0, -1, -2], "1.4583": [0, -1, -2], "1.5": [0, -1, -2], "1.5833": [0, -1, -2], "1.6667": [0, -1, -2], "1.75": [0, -1, -2], "1.8333": [0, -1, -2], "1.9167": [0, -1, -2], "2.0": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.49, -16.76, -15.51], "0.0833": [-29.49, -16.76, -15.51], "0.125": [-29.49, -16.76, -15.51], "0.1667": [-28.24, -16.76, -15.51], "0.2083": [-26.99, -16.76, -15.51], "0.25": [-27.27, -16.76, -15.51], "0.3333": [-27.83, -16.76, -15.51], "0.4167": [-28.38, -16.76, -15.51], "0.5": [-28.94, -16.76, -15.51], "0.5833": [-29.49, -16.76, -15.51], "0.6667": [-29.49, -16.76, -15.51], "0.75": [-29.49, -16.76, -15.51], "0.8333": [-29.49, -16.76, -15.51], "0.9167": [-29.49, -16.76, -15.51], "1.0": [-29.49, -16.76, -15.51], "1.0833": [-26.99, -16.76, -15.51], "1.1667": [-27.55, -16.76, -15.51], "1.25": [-28.1, -16.76, -15.51], "1.3333": [-28.66, -16.76, -15.51], "1.4167": [-29.21, -16.76, -15.51], "1.4583": [-29.49, -16.76, -15.51], "1.5": [-29.49, -16.76, -15.51], "1.5833": [-29.49, -16.76, -15.51], "1.6667": [-29.49, -16.76, -15.51], "1.75": [-29.49, -16.76, -15.51], "1.8333": [-29.49, -16.76, -15.51], "1.9167": [-29.49, -16.76, -15.51], "2.0": [-29.49, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0, 0, -2], "0.125": [0, 0, -2], "0.1667": [0, 0, -2], "0.25": [0, 0, -2], "0.3333": [0, 0, -2], "0.4167": [0, 0, -2], "0.5": [0, 0, -2], "0.5833": [0, 0, -2], "0.6667": [0, 0, -2], "0.75": [0, 0, -2], "0.8333": [0, 0, -2], "0.9167": [0, 0, -2], "1.0": [0, 0, -2], "1.0833": [0, 0, -2], "1.1667": [0, 0, -2], "1.25": [0, 0, -2], "1.3333": [0, 0, -2], "1.4167": [0, 0, -2], "1.4583": [0, 0, -2], "1.5": [0, 0, -2], "1.5833": [0, 0, -2], "1.6667": [0, 0, -2], "1.75": [0, 0, -2], "1.8333": [0, 0, -2], "1.9167": [0, 0, -2], "2.0": [0, 0, -2]}}, "sledge_hammer": {"rotation": [13.55, -60.57, 9.71], "position": [2, -2, 0]}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [1.3, 0, -22.5], "0.125": [0, 0, -22.5], "0.1667": [-9.84, 0, -22.5], "0.2083": [-17.5, 0, -22.5], "0.25": [-17, 0, -22.5], "0.3333": [-13.61, 0, -22.5], "0.4167": [-8.5, 0, -22.5], "0.5": [-3.38, 0, -22.5], "0.5833": [0, 0, -22.5], "0.6667": [1.12, 0, -22.5], "0.75": [1.26, 0, -22.5], "0.8333": [0.84, 0, -22.5], "0.9167": [0.28, 0, -22.5], "1.0": [0, 0, -22.5], "1.0833": [1.3, 0, -22.5], "1.125": [0, 0, -22.5], "1.1667": [-9.84, 0, -22.5], "1.2083": [-17.5, 0, -22.5], "1.25": [-17, 0, -22.5], "1.3333": [-13.61, 0, -22.5], "1.4167": [-8.5, 0, -22.5], "1.5": [-3.38, 0, -22.5], "1.5833": [0, 0, -22.5], "1.6667": [1.12, 0, -22.5], "1.75": [1.26, 0, -22.5], "1.8333": [0.84, 0, -22.5], "1.9167": [0.28, 0, -22.5], "2.0": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [1.3, 0, 22.5], "0.125": [0, 0, 22.5], "0.1667": [-9.84, 0, 22.5], "0.2083": [-17.5, 0, 22.5], "0.25": [-17, 0, 22.5], "0.3333": [-13.61, 0, 22.5], "0.4167": [-8.5, 0, 22.5], "0.5": [-3.38, 0, 22.5], "0.5833": [0, 0, 22.5], "0.6667": [1.12, 0, 22.5], "0.75": [1.26, 0, 22.5], "0.8333": [0.84, 0, 22.5], "0.9167": [0.28, 0, 22.5], "1.0": [0, 0, 22.5], "1.0833": [1.3, 0, 22.5], "1.125": [0, 0, 22.5], "1.1667": [-9.84, 0, 22.5], "1.2083": [-17.5, 0, 22.5], "1.25": [-17, 0, 22.5], "1.3333": [-13.61, 0, 22.5], "1.4167": [-8.5, 0, 22.5], "1.5": [-3.38, 0, 22.5], "1.5833": [0, 0, 22.5], "1.6667": [1.12, 0, 22.5], "1.75": [1.26, 0, 22.5], "1.8333": [0.84, 0, 22.5], "1.9167": [0.28, 0, 22.5], "2.0": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-0.29, 0, 0], "0.1667": [-0.52, 0, 0], "0.25": [-0.7, 0, 0], "0.3333": [-0.83, 0, 0], "0.4167": [-0.91, 0, 0], "0.5": [-0.94, 0, 0], "0.5833": [-0.91, 0, 0], "0.6667": [-0.83, 0, 0], "0.75": [-0.7, 0, 0], "0.8333": [-0.52, 0, 0], "0.9167": [-0.29, 0, 0], "1.0": [0, 0, 0], "1.0833": [0.99, 0, 0], "1.1667": [2.5, 0, 0], "1.25": [4.22, 0, 0], "1.3333": [5.83, 0, 0], "1.4167": [7.03, 0, 0], "1.5": [7.5, 0, 0], "1.5833": [7.03, 0, 0], "1.6667": [5.83, 0, 0], "1.75": [4.22, 0, 0], "1.8333": [2.5, 0, 0], "1.9167": [0.99, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [1, 2, -2], "0.0833": [1, 0.63, -2.19], "0.125": [1, 0, -2], "0.1667": [1, -0.09, -2.02], "0.25": [1, -0.15, -2.15], "0.3333": [1, -0.11, -2.27], "0.4167": [1, -0.04, -2.27], "0.5": [1, 0, -2], "0.5833": [1, -0.01, -1.53], "0.6667": [1, -0.04, -0.85], "0.75": [1, -0.06, -0.06], "0.8333": [1, -0.07, 0.74], "0.9167": [1, -0.06, 1.46], "1.0": [1, 0, 2], "1.0833": [1, 0.11, 2.41], "1.1667": [1, 0.26, 2.78], "1.25": [1, 0.44, 3.06], "1.3333": [1, 0.63, 3.22], "1.4167": [1, 0.82, 3.22], "1.5": [1, 1, 3], "1.5833": [1, 1.2, 2.46], "1.6667": [1, 1.44, 1.59], "1.75": [1, 1.69, 0.56], "1.8333": [1, 1.89, -0.48], "1.9167": [1, 2.01, -1.39], "2.0": [1, 2, -2]}}, "right_leg": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [-1, 0, 2], "0.0833": [-1, 0.11, 1.93], "0.1667": [-1, 0.26, 1.75], "0.25": [-1, 0.45, 1.47], "0.3333": [-1, 0.67, 1.11], "0.4167": [-1, 0.89, 0.7], "0.5": [-1, 1.13, 0.25], "0.5833": [-1, 1.35, -0.21], "0.6667": [-1, 1.56, -0.67], "0.75": [-1, 1.73, -1.09], "0.8333": [-1, 1.88, -1.47], "0.9167": [-1, 1.97, -1.78], "1.0": [-1, 2, -2], "1.0833": [-1, 0.67, -2.15], "1.125": [-1, 0, -2], "1.1667": [-1, -0.09, -2.02], "1.25": [-1, -0.15, -2.15], "1.3333": [-1, -0.11, -2.27], "1.4167": [-1, -0.04, -2.27], "1.5": [-1, 0, -2], "1.5833": [-1, -0.02, -1.47], "1.6667": [-1, -0.07, -0.67], "1.75": [-1, -0.12, 0.25], "1.8333": [-1, -0.15, 1.11], "1.9167": [-1, -0.12, 1.75], "2.0": [-1, 0, 2]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [4.22, 0, 0], "0.2083": [7.5, 0, 0], "0.25": [7.28, 0, 0], "0.3333": [5.83, 0, 0], "0.4167": [3.64, 0, 0], "0.5": [1.45, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.125": [0, 0, 0], "1.1667": [4.22, 0, 0], "1.2083": [7.5, 0, 0], "1.25": [7.28, 0, 0], "1.3333": [5.83, 0, 0], "1.4167": [3.64, 0, 0], "1.5": [1.45, 0, 0], "1.5833": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.run": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time + (q.delta_time * q.modified_move_speed * 3)", "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.0833": [9.01, -1.91, -0.34], "0.125": [10.01, -2.46, -0.43], "0.1667": [8.01, -1.38, -0.24], "0.2083": [5, 0, 0], "0.25": [3.59, 0.18, 0.03], "0.3333": [1.89, 0, 0], "0.4167": [5, 0, 0], "0.5": [9.01, 1.91, 0.34], "0.5417": [10.01, 2.46, 0.43], "0.5833": [9.62, 2.25, 0.4], "0.6667": [7.35, 1.04, 0.18], "0.75": [5, 0, 0], "0.8333": [2.4, -0.09, -0.02], "0.875": [1.89, 0, 0], "0.9167": [2.4, 0.09, 0.02], "1.0": [5, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.0833": [-42.5, 0, 0], "0.125": [-42.5, 0, 0], "0.1667": [-40, 0, 0], "0.2083": [-37.5, 0, 0], "0.25": [-40, 0, 0], "0.2917": [-42.5, 0, 0], "0.3333": [-42.5, 0, 0], "0.4167": [-42.5, 0, 0], "0.5": [-42.5, 0, 0], "0.5417": [-42.5, 0, 0], "0.5833": [-40, 0, 0], "0.625": [-37.5, 0, 0], "0.6667": [-40, 0, 0], "0.7083": [-42.5, 0, 0], "0.75": [-42.5, 0, 0], "0.8333": [-42.5, 0, 0], "0.9167": [-42.5, 0, 0], "1.0": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0, -1, -2], "0.125": [0, -1, -2], "0.1667": [0, -1, -2], "0.25": [0, -1, -2], "0.2917": [0, -1, -2], "0.3333": [0, -1, -2], "0.4167": [0, -1, -2], "0.5": [0, -1, -2], "0.5417": [0, -1, -2], "0.5833": [0, -1, -2], "0.6667": [0, -1, -2], "0.7083": [0, -1, -2], "0.75": [0, -1, -2], "0.8333": [0, -1, -2], "0.9167": [0, -1, -2], "1.0": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.49, -16.76, -15.51], "0.0833": [-29.49, -16.76, -15.51], "0.125": [-29.49, -16.76, -15.51], "0.1667": [-28.24, -16.76, -15.51], "0.2083": [-26.99, -16.76, -15.51], "0.25": [-28.24, -16.76, -15.51], "0.2917": [-29.49, -16.76, -15.51], "0.3333": [-29.49, -16.76, -15.51], "0.4167": [-29.49, -16.76, -15.51], "0.5": [-26.99, -16.76, -15.51], "0.5833": [-27.99, -16.76, -15.51], "0.6667": [-28.99, -16.76, -15.51], "0.7083": [-29.49, -16.76, -15.51], "0.75": [-29.49, -16.76, -15.51], "0.8333": [-29.49, -16.76, -15.51], "0.9167": [-29.49, -16.76, -15.51], "1.0": [-29.49, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0, 0, -2], "0.125": [0, 0, -2], "0.1667": [0, 0, -2], "0.25": [0, 0, -2], "0.2917": [0, 0, -2], "0.3333": [0, 0, -2], "0.4167": [0, 0, -2], "0.5": [0, 0, -2], "0.5833": [0, 0, -2], "0.6667": [0, 0, -2], "0.7083": [0, 0, -2], "0.75": [0, 0, -2], "0.8333": [0, 0, -2], "0.9167": [0, 0, -2], "1.0": [0, 0, -2]}}, "sledge_hammer": {"rotation": [13.55, -60.57, 9.71], "position": [2, -2, 0]}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [1.3, 0, -22.5], "0.125": [0, 0, -22.5], "0.1667": [-9.84, 0, -22.5], "0.2083": [-17.5, 0, -22.5], "0.25": [-9.84, 0, -22.5], "0.2917": [0, 0, -22.5], "0.3333": [1.3, 0, -22.5], "0.4167": [0, 0, -22.5], "0.5": [1.3, 0, -22.5], "0.5417": [0, 0, -22.5], "0.5833": [-9.84, 0, -22.5], "0.625": [-17.5, 0, -22.5], "0.6667": [-15.96, 0, -22.5], "0.75": [-7.42, 0, -22.5], "0.8333": [0, 0, -22.5], "0.9167": [1.09, 0, -22.5], "1.0": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [1.3, 0, 22.5], "0.125": [0, 0, 22.5], "0.1667": [-9.84, 0, 22.5], "0.2083": [-17.5, 0, 22.5], "0.25": [-9.84, 0, 22.5], "0.2917": [0, 0, 22.5], "0.3333": [1.3, 0, 22.5], "0.4167": [0, 0, 22.5], "0.5": [1.3, 0, 22.5], "0.5417": [0, 0, 22.5], "0.5833": [-9.84, 0, 22.5], "0.625": [-17.5, 0, 22.5], "0.6667": [-15.96, 0, 22.5], "0.75": [-7.42, 0, 22.5], "0.8333": [0, 0, 22.5], "0.9167": [1.09, 0, 22.5], "1.0": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-0.6, 0, 0], "0.1667": [-0.9, 0, 0], "0.25": [-0.9, 0, 0], "0.3333": [-0.6, 0, 0], "0.4167": [0, 0, 0], "0.5": [1.7, 0, 0], "0.5833": [4.22, 0, 0], "0.6667": [6.5, 0, 0], "0.75": [7.5, 0, 0], "0.8333": [5.83, 0, 0], "0.9167": [2.5, 0, 0], "1.0": [0, 0, 0]}, "position": {"0.0": [1, 2, -2], "0.0833": [1, 0.63, -2.19], "0.125": [1, 0, -2], "0.1667": [1, -0.13, -2.25], "0.2083": [1, 0, -2], "0.25": [1, -0.02, -1.41], "0.3333": [1, -0.07, 0.42], "0.4167": [1, 0, 2], "0.5": [1, 0.18, 2.6], "0.5833": [1, 0.44, 3.06], "0.6667": [1, 0.73, 3.24], "0.75": [1, 1, 3], "0.8333": [1, 1.44, 1.59], "0.9167": [1, 1.89, -0.48], "1.0": [1, 2, -2]}}, "right_leg": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}, "position": {"0.0": [-1, 0, 2], "0.0833": [-1, 0.34, 1.65], "0.1667": [-1, 0.85, 0.78], "0.25": [-1, 1.39, -0.3], "0.3333": [-1, 1.82, -1.33], "0.4167": [-1, 2, -2], "0.5": [-1, 0.67, -2.15], "0.5417": [-1, 0, -2], "0.5833": [-1, -0.13, -2.06], "0.6667": [-1, -0.1, -2.29], "0.75": [-1, 0, -2], "0.8333": [-1, -0.07, -0.67], "0.9167": [-1, -0.15, 1.11], "1.0": [-1, 0, 2]}}, "eyes": {"scale": [1, 0.5, 1]}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [4.22, 0, 0], "0.2083": [7.5, 0, 0], "0.25": [4.22, 0, 0], "0.2917": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5417": [0, 0, 0], "0.5833": [4.22, 0, 0], "0.625": [7.5, 0, 0], "0.6667": [6.84, 0, 0], "0.75": [3.18, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.slam_attack": {"loop": "hold_on_last_frame", "animation_length": 1.5, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.0833": [0.42, 0, 0], "0.1667": [-4.17, 0, 0], "0.25": [-8.12, 0, 0], "0.3333": [-10.83, 0, 0], "0.4167": [-11.67, 0, 0], "0.5": [-10, 0, 0], "0.5833": [5.83, 0, 0], "0.6667": [29.17, 0, 0], "0.75": [42.5, 0, 0], "0.8333": [42.16, 0, 0], "0.9167": [39.78, 0, 0], "1.0": [35.83, 0, 0], "1.0833": [30.77, 0, 0], "1.1667": [25.06, 0, 0], "1.25": [19.17, 0, 0], "1.3333": [13.55, 0, 0], "1.4167": [8.67, 0, 0], "1.5": [5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.7917": [0, -2, 0], "0.8333": [0, 0, 0], "0.875": [0, -2, 0], "0.9167": [0, -1.87, 0], "1.0": [0, -1.6, 0], "1.0833": [0, -1.33, 0], "1.1667": [0, -1.07, 0], "1.25": [0, -0.8, 0], "1.3333": [0, -0.53, 0], "1.4167": [0, -0.27, 0], "1.5": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.0833": [-58.11, -1.46, 0.69], "0.1667": [-83.24, -2.42, 1.24], "0.25": [-112.94, -2.82, 1.54], "0.3333": [-142.26, -2.58, 1.49], "0.4167": [-166.26, -1.67, 1.01], "0.5": [-180, 0, 0], "0.5833": [-125.98, 24.72, -15.69], "0.6667": [-94.38, 24.08, -13.26], "0.75": [-69.54, 20.35, -8.87], "0.8333": [-67.7, 21.35, -9], "0.9167": [-69.54, 20.35, -8.87], "1.0": [-65.66, 18.33, -7.99], "1.0833": [-58.99, 15.42, -6.72], "1.1667": [-51.21, 11.98, -5.22], "1.25": [-43.99, 8.36, -3.65], "1.3333": [-39.02, 4.92, -2.15], "1.4167": [-37.96, 2.02, -0.88], "1.5": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0.35, -0.53, -1.85], "0.1667": [0.56, 0, -2], "0.25": [0.63, 0.5, -2.37], "0.3333": [0.56, 0.89, -2.89], "0.4167": [0.35, 1.08, -3.46], "0.5": [0, 1, -4], "0.5833": [-1.48, -0.33, -5.33], "0.6667": [-3.52, -2.44, -6.89], "0.75": [-5, -4, -8], "0.8333": [-5.62, -4.5, -8.62], "0.9167": [-5, -4, -8], "1.0": [-4.5, -3.72, -7.39], "1.0833": [-3.79, -3.33, -6.49], "1.1667": [-2.94, -2.87, -5.43], "1.25": [-2.06, -2.37, -4.33], "1.3333": [-1.21, -1.87, -3.31], "1.4167": [-0.5, -1.4, -2.49], "1.5": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.49, -16.76, -15.51], "0.0833": [-40.57, -19.97, -12.6], "0.1667": [-59.87, -21.3, -10.38], "0.25": [-83.15, -21.2, -9.19], "0.3333": [-106.18, -20.12, -9.41], "0.4167": [-124.71, -18.49, -11.4], "0.5": [-134.49, -16.76, -15.51], "0.5833": [-98.97, -5.38, -62.17], "0.625": [-77.48, 2.78, -79.07], "0.6667": [-70.42, 13.71, -76.04], "0.75": [-67.92, 34.79, -52.96], "0.8333": [-69.73, 40.01, -53.66], "0.9167": [-67.92, 34.79, -52.96], "1.0": [-63.19, 29.68, -49.24], "1.0833": [-55.56, 22.31, -43.89], "1.1667": [-46.61, 13.6, -37.56], "1.25": [-37.94, 4.43, -30.9], "1.3333": [-31.14, -4.29, -24.57], "1.4167": [-27.79, -11.65, -19.22], "1.5": [-29.49, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0.45, 0.14, -1.99], "0.1667": [1.33, 0.22, -2.22], "0.25": [2.44, 0.25, -2.62], "0.3333": [3.56, 0.22, -3.11], "0.4167": [4.48, 0.14, -3.6], "0.5": [5, 0, -4], "0.5833": [4.78, -0.59, -4.74], "0.6667": [3.78, -1.41, -5.48], "0.75": [3, -2, -6], "0.8333": [3.06, -2.25, -6.37], "0.9167": [3, -2, -6], "1.0": [2.66, -1.8, -5.59], "1.0833": [2.13, -1.52, -4.97], "1.1667": [1.5, -1.18, -4.25], "1.25": [0.88, -0.82, -3.5], "1.3333": [0.36, -0.48, -2.82], "1.4167": [0.03, -0.2, -2.29], "1.5": [0, 0, -2]}}, "sledge_hammer": {"rotation": {"0.0": [13.55, -60.57, 9.71], "0.0833": [8.88, -61.64, 11.48], "0.1667": [1.91, -63.6, 12.91], "0.25": [-6.26, -66.05, 14.3], "0.3333": [-14.56, -68.58, 15.96], "0.4167": [-21.88, -70.78, 18.19], "0.5": [-27.14, -72.24, 21.3], "0.5833": [-35.9, -72.86, 48.56], "0.625": [-32.86, -71.94, 53.45], "0.6667": [-20.36, -70.3, 39.03], "0.75": [10.74, -66.49, -3.25], "0.8333": [13.87, -66.3, -7.93], "0.9167": [12.15, -66.73, -6.31], "1.0": [10.74, -66.49, -3.25], "1.0833": [11.55, -65.64, -1.83], "1.1667": [13.08, -64.3, 0.16], "1.25": [14.69, -62.8, 2.5], "1.3333": [15.73, -61.46, 5.01], "1.4167": [15.56, -60.61, 7.47], "1.5": [13.55, -60.57, 9.71]}, "position": {"0.0": [2, -2, 0], "0.0833": [2, -1.93, -0.14], "0.1667": [2, -1.89, -0.22], "0.25": [2, -1.87, -0.25], "0.3333": [2, -1.89, -0.22], "0.4167": [2, -1.93, -0.14], "0.5": [2, -2, 0], "0.5833": [2, -2.3, 0.59], "0.6667": [2, -2.7, 1.41], "0.75": [2, -3, 2], "0.8333": [2, -3.11, 2.22], "0.9167": [2, -3.11, 2.22], "1.0": [2, -3, 2], "1.0833": [2, -2.88, 1.76], "1.1667": [2, -2.7, 1.41], "1.25": [2, -2.5, 1], "1.3333": [2, -2.3, 0.59], "1.4167": [2, -2.12, 0.24], "1.5": [2, -2, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [3.56, 0, -22.5], "0.1667": [5.56, 0, -22.5], "0.25": [6.09, 0, -22.5], "0.3333": [5.28, 0, -22.5], "0.4167": [3.21, 0, -22.5], "0.5": [0, 0, -22.5], "0.5833": [-13.06, 0, -22.5], "0.6667": [-31.11, 0, -22.5], "0.75": [-45, 0, -22.5], "0.8333": [-54.84, 0, -22.5], "0.9167": [-52.5, 0, -22.5], "1.0": [-42.36, 0, -22.5], "1.0833": [-26.72, 0, -22.5], "1.1667": [-10.84, 0, -22.5], "1.25": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [3.56, 0, 22.5], "0.1667": [5.56, 0, 22.5], "0.25": [6.09, 0, 22.5], "0.3333": [5.28, 0, 22.5], "0.4167": [3.21, 0, 22.5], "0.5": [0, 0, 22.5], "0.5833": [-13.06, 0, 22.5], "0.6667": [-31.11, 0, 22.5], "0.75": [-45, 0, 22.5], "0.8333": [-54.84, 0, 22.5], "0.9167": [-52.5, 0, 22.5], "1.0": [-42.36, 0, 22.5], "1.0833": [-26.72, 0, 22.5], "1.1667": [-10.84, 0, 22.5], "1.25": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.0833": [0.19, -22.5, 0], "0.1667": [0.65, -22.5, 0], "0.25": [1.25, -22.5, 0], "0.3333": [1.85, -22.5, 0], "0.4167": [2.31, -22.5, 0], "0.5": [2.5, -22.5, 0], "0.5833": [1.85, -22.5, 0], "0.6667": [0.65, -22.5, 0], "0.75": [0, -22.5, 0]}, "position": {"0.0": [1, 0, 0], "0.0833": [1, 0, 0], "0.1667": [1, 0, 0], "0.25": [1, 0, 0], "0.3333": [1, 0, 0], "0.4167": [1, 0, 0], "0.5": [1, 0, 0], "0.5833": [1, 0, 0], "0.6667": [1, 0, 0], "0.75": [1, 0, 0]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0.19, 22.5, 0], "0.1667": [0.65, 22.5, 0], "0.25": [1.25, 22.5, 0], "0.3333": [1.85, 22.5, 0], "0.4167": [2.31, 22.5, 0], "0.5": [2.5, 22.5, 0], "0.5833": [1.85, 22.5, 0], "0.6667": [0.65, 22.5, 0], "0.75": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, 0], "0.0833": [-1, 0, 0], "0.1667": [-1, 0, 0], "0.25": [-1, 0, 0], "0.3333": [-1, 0, 0], "0.4167": [-1, 0, 0], "0.5": [-1, 0, 0], "0.5833": [-1, 0, 0], "0.6667": [-1, 0, 0], "0.75": [-1, 0, 0]}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.0833": [1, 1.05, 1], "0.1667": [1, 1.1, 1], "0.25": [1, 1.15, 1], "0.3333": [1, 1.2, 1], "0.4167": [1, 1.25, 1], "0.5": [1, 1.3, 1], "0.5833": [1, 0.9, 1], "0.6667": [1, 0.5, 1], "0.75": [1, 0.1, 1], "0.8333": [1, 0.25, 1], "0.9167": [1, 0.4, 1], "1.0": [1, 0.55, 1], "1.0833": [1, 0.7, 1], "1.1667": [1, 0.85, 1], "1.25": [1, 1, 1]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [2.71, 0, 0], "0.1667": [6.67, 0, 0], "0.25": [11.25, 0, 0], "0.3333": [15.83, 0, 0], "0.4167": [19.79, 0, 0], "0.5": [22.5, 0, 0], "0.5833": [26.72, 0, 0], "0.6667": [22.5, 0, 0], "0.75": [-22.5, 0, 0], "0.8333": [-19.17, 0, 0], "0.9167": [-8.33, 0, 0], "1.0": [0, 0, 0], "1.0833": [1.04, 0, 0], "1.1667": [0.83, 0, 0], "1.25": [0, 0, 0], "1.3333": [-0.83, 0, 0], "1.4167": [-1.04, 0, 0], "1.5": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.sweep_attack": {"loop": "hold_on_last_frame", "animation_length": 1.375, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.0833": [3.16, -8.01, -0.81], "0.1667": [0.96, -17.33, -1.22], "0.25": [-1.36, -27.03, -1.32], "0.3333": [-3.51, -36.15, -1.18], "0.4167": [-5.25, -43.74, -0.89], "0.5": [-6.3, -48.86, -0.52], "0.5833": [-6.4, -50.54, -0.16], "0.625": [-6.01, -49.8, -0.01], "0.6667": [-2.47, -39.62, 0.45], "0.75": [10.93, 2.59, 1.79], "0.8333": [21.52, 39.82, 4.18], "0.9167": [22.9, 51.8, 7.67], "1.0": [21.44, 57.74, 12.12], "1.0833": [18.75, 57.78, 15.18], "1.125": [17.45, 55.65, 15.45], "1.1667": [15.84, 50.44, 14.25], "1.25": [11.66, 31.93, 8.43], "1.3333": [7.09, 9.76, 1.99], "1.375": [5, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.0833": [-54.91, -0.06, 1.44], "0.1667": [-68.95, -0.07, 1.62], "0.25": [-83.53, -0.05, 1.08], "0.3333": [-97.58, -0.02, 0.36], "0.4167": [-110, 0, 0], "0.5": [-141.39, -0.79, 0.75], "0.5833": [-162.06, -1.05, 1], "0.625": [-155, 0, 0], "0.6667": [-126.76, 2.74, -2.27], "0.75": [-29.12, 11.34, -9.26], "0.8333": [45.24, 16.39, -15.63], "0.9167": [50.75, 13.88, -19.09], "1.0": [33.6, 8.23, -22.06], "1.0833": [10.67, 2.68, -23.12], "1.125": [2.31, 0.96, -22.48], "1.1667": [-4.79, -0.05, -20.17], "1.25": [-18.56, -0.49, -11.67], "1.3333": [-33.7, -0.06, -2.79], "1.375": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.0833": [0, -0.98, -2.29], "0.1667": [0, -0.95, -2.38], "0.25": [0, -0.93, -2.34], "0.3333": [0, -0.94, -2.19], "0.4167": [0, -1, -2], "0.5": [0, -1.42, -1.34], "0.5833": [0, -1.91, -0.43], "0.625": [0, -2, 0], "0.6667": [0, -1.91, 0.5], "0.75": [0, -1.42, 1.63], "0.8333": [0, -1, 2], "0.9167": [0, -0.94, 1.68], "1.0": [0, -0.93, 1.08], "1.0833": [0, -0.93, 0.33], "1.1667": [0, -0.95, -0.48], "1.25": [0, -0.98, -1.23], "1.3333": [0, -1, -1.81], "1.375": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.49, -16.76, -15.51], "0.0833": [-32.85, -23.19, -17.74], "0.1667": [-42.33, -26.31, -18.65], "0.25": [-54.43, -27.66, -18.9], "0.3333": [-65.68, -28.76, -19.18], "0.4167": [-72.59, -31.15, -20.15], "0.5": [-73.03, -45.25, -26.54], "0.5833": [-67.2, -56.1, -32.04], "0.625": [-65.29, -52.23, -30.92], "0.6667": [-64.3, -37.32, -25.22], "0.75": [-62.67, 13.51, -5.23], "0.8333": [-63.67, 54.75, 11.07], "0.9167": [-69.29, 64.63, 14.91], "1.0": [-77.51, 65.36, 15.14], "1.0833": [-82.13, 59.43, 12.83], "1.125": [-81.17, 54.75, 11.07], "1.1667": [-75.46, 46.31, 7.93], "1.25": [-53.73, 19.89, -1.93], "1.3333": [-33.42, -7.32, -12.04], "1.375": [-29.49, -16.76, -15.51]}, "position": {"0.0": [0, 0, -2], "0.0833": [0, 0, -2], "0.1667": [0, 0, -2], "0.25": [0, 0, -2], "0.3333": [0, 0, -2], "0.4167": [0, 0, -2], "0.5": [0, 0, -2], "0.5833": [0, 0, -2], "0.6667": [0, 0, -2], "0.75": [0, 0, -2], "0.8333": [0, 0, -2], "0.9167": [0, 0, -2], "1.0": [0, 0, -2], "1.0833": [0, 0, -2], "1.1667": [0, 0, -2], "1.25": [0, 0, -2], "1.3333": [0, 0, -2], "1.375": [0, 0, -2]}}, "sledge_hammer": {"rotation": {"0.0": [13.55, -60.57, 9.71], "0.0833": [10.79, -60.57, 9.71], "0.1667": [10.67, -60.57, 9.71], "0.25": [11.93, -60.57, 9.71], "0.3333": [13.31, -60.57, 9.71], "0.4167": [13.55, -60.57, 9.71], "0.5": [8.21, -60.57, 9.71], "0.5833": [3.83, -60.57, 9.71], "0.625": [6.05, -60.57, 9.71], "0.6667": [14.27, -60.57, 9.71], "0.75": [41.69, -60.57, 9.71], "0.8333": [58.55, -60.57, 9.71], "0.9167": [56.54, -60.57, 9.71], "1.0": [50.41, -60.57, 9.71], "1.0833": [41.72, -60.57, 9.71], "1.1667": [32.01, -60.57, 9.71], "1.25": [22.85, -60.57, 9.71], "1.3333": [15.8, -60.57, 9.71], "1.375": [13.55, -60.57, 9.71]}, "position": {"0.0": [2, -2, 0], "0.0833": [2, -2, 0], "0.1667": [2, -2, 0], "0.25": [2, -2, 0], "0.3333": [2, -2, 0], "0.4167": [2, -2, 0], "0.5": [2, -2, 0], "0.5833": [2, -2, 0], "0.625": [2, -2, 0], "0.6667": [2, -2, 0], "0.75": [2, -2, 0], "0.8333": [2, -2, 0], "0.9167": [2, -2, 0], "1.0": [2, -2, 0], "1.0833": [2, -2, 0], "1.1667": [2, -2, 0], "1.25": [2, -2, 0], "1.3333": [2, -2, 0], "1.375": [2, -2, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [0.86, 0, -22.5], "0.1667": [0.89, 0, -22.5], "0.25": [0.4, 0, -22.5], "0.3333": [-0.3, 0, -22.5], "0.4167": [-0.9, 0, -22.5], "0.5": [-1.11, 0, -22.5], "0.5833": [-0.61, 0, -22.5], "0.625": [0, 0, -22.5], "0.6667": [3.13, 0, -22.5], "0.75": [12.73, 0, -22.5], "0.8333": [22.5, 0, -22.5], "0.9167": [36.81, 0, -22.5], "1.0": [40.66, 0, -22.5], "1.0833": [9.87, 0, -22.5], "1.1667": [-20.61, 0, -22.5], "1.25": [-17.27, 0, -22.5], "1.3333": [-4.11, 0, -22.5], "1.375": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [-0.86, 0, 22.5], "0.1667": [-0.89, 0, 22.5], "0.25": [-0.4, 0, 22.5], "0.3333": [0.3, 0, 22.5], "0.4167": [0.9, 0, 22.5], "0.5": [1.11, 0, 22.5], "0.5833": [0.61, 0, 22.5], "0.625": [0, 0, 22.5], "0.6667": [-3.13, 0, 22.5], "0.75": [-12.73, 0, 22.5], "0.8333": [-22.5, 0, 22.5], "0.9167": [-36.81, 0, 22.5], "1.0": [-40.66, 0, 22.5], "1.0833": [-9.87, 0, 22.5], "1.1667": [20.61, 0, 22.5], "1.25": [17.27, 0, 22.5], "1.3333": [4.11, 0, 22.5], "1.375": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.0833": [0, -24.84, 0], "0.1667": [0, -27.78, 0], "0.25": [0, -30.78, 0], "0.3333": [0, -33.33, 0], "0.4167": [0, -34.91, 0], "0.5": [0, -35, 0], "0.5833": [0, -28.89, 0], "0.6667": [0, -18.89, 0], "0.75": [0, -12.5, 0], "0.8333": [0, -12.19, 0], "0.9167": [0, -12.77, 0], "1.0": [0, -14.04, 0], "1.0833": [0, -15.78, 0], "1.1667": [0, -17.78, 0], "1.25": [0, -19.82, 0], "1.3333": [0, -21.69, 0], "1.375": [0, -22.5, 0]}, "position": {"0.0": [1, 0, 0], "0.0833": [1, 0, 0], "0.1667": [1, 0, 0], "0.25": [1, 0, 0], "0.3333": [1, 0, 0], "0.4167": [1, 0, 0], "0.5": [1, 0, 0], "0.5833": [1, 0, 0], "0.6667": [1, 0, 0], "0.75": [1, 0, 0], "0.8333": [1, 0, 0], "0.9167": [1, 0, 0], "1.0": [1, 0, 0], "1.0833": [1, 0, 0], "1.1667": [1, 0, 0], "1.25": [1, 0, 0], "1.3333": [1, 0, 0], "1.375": [1, 0, 0]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 18.66, 0], "0.1667": [0, 13.61, 0], "0.25": [0, 8.28, 0], "0.3333": [0, 3.61, 0], "0.4167": [0, 0.54, 0], "0.5": [0, 0, 0], "0.5833": [0, 9.17, 0], "0.6667": [0, 24.72, 0], "0.75": [0, 35, 0], "0.8333": [0, 35.79, 0], "0.9167": [0, 35.33, 0], "1.0": [0, 33.9, 0], "1.0833": [0, 31.76, 0], "1.1667": [0, 29.17, 0], "1.25": [0, 26.4, 0], "1.3333": [0, 23.72, 0], "1.375": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, 0], "0.0833": [-1, 0, 0], "0.1667": [-1, 0, 0], "0.25": [-1, 0, 0], "0.3333": [-1, 0, 0], "0.4167": [-1, 0, 0], "0.5": [-1, 0, 0], "0.5833": [-1, 0, 0], "0.6667": [-1, 0, 0], "0.75": [-1, 0, 0], "0.8333": [-1, 0, 0], "0.9167": [-1, 0, 0], "1.0": [-1, 0, 0], "1.0833": [-1, 0, 0], "1.1667": [-1, 0, 0], "1.25": [-1, 0, 0], "1.3333": [-1, 0, 0], "1.375": [-1, 0, 0]}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.0833": [1, 1, 1], "0.1667": [1, 1, 1], "0.25": [1, 1, 1], "0.3333": [1, 1, 1], "0.4167": [1, 1, 1], "0.5": [1, 1, 1], "0.5833": [1, 0.7, 1], "0.6667": [1, 0.4, 1], "0.75": [1, 0.1, 1], "0.8333": [1, 0.1, 1], "0.9167": [1, 0.1, 1], "1.0": [1, 0.1, 1], "1.0833": [1, 0.3, 1], "1.1667": [1, 0.5, 1], "1.25": [1, 0.7, 1], "1.3333": [1, 0.9, 1], "1.375": [1, 1, 1]}}}}, "animation.ptd_bb.piglin_maruarder.damaged": {"loop": true, "animation_length": 0.5, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.125": [10, 0, 0], "0.25": [-10, 0, 0], "0.5": [5, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.25": [-52.5, 0, 0], "0.5": [-42.5, 0, 0]}, "position": {"0.0": [0, -1, -2], "0.5": [0, -1, -2]}}, "right_arm": {"rotation": {"0.0": [-29.4919, -16.75858, -15.50706], "0.25": [-39.4919, -16.75858, -15.50706], "0.5": [-29.4919, -16.75858, -15.50706]}, "position": {"0.0": [0, 0, -2], "0.5": [0, 0, -2]}}, "sledge_hammer": {"rotation": [13.54545, -60.57268, 9.70542], "position": [2, -2, 0]}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.25": [-22.5, 0, -22.5], "0.5": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.25": [-22.5, 0, 22.5], "0.5": [0, 0, 22.5]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.125": [0, -22.5, 0], "0.25": [-2.5, -22.5, 0], "0.3333": [-2.5, -22.5, 0], "0.5": [0, -22.5, 0]}, "position": {"0.0": [1, 0, 0], "0.125": [1, 0, 0], "0.5": [1, 0, 0]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.125": [0, 22.5, 0], "0.25": [-2.5, 22.5, 0], "0.3333": [-2.5, 22.5, 0], "0.5": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, 0], "0.125": [-1, 0, 0], "0.5": [-1, 0, 0]}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.25": [1, 0.5, 1], "0.5": [1, 1, 1]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.25": [22.5, 0, 0], "0.5": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.death": {"loop": "hold_on_last_frame", "animation_length": 2, "bones": {"torso": {"rotation": {"0.0": [5, 0, 0], "0.5": [-17.5, 0, 0], "0.75": [5, 0, 0], "0.9167": [90, 0, 0], "1.0417": [90, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [0, -1, -2], "0.5": [0, -2, -6], "0.75": [0, -11, -6], "1.0417": [0, -12, -6]}}, "left_arm": {"rotation": {"0.0": [-42.5, 0, 0], "0.25": [-120.59871, 10.80355, -6.32526], "0.625": [-138.09871, 10.80355, -6.32526], "0.875": [-175.67642, 0.83237, -7.08889], "1.0": [-178.17642, 0.83237, -7.08889], "1.0417": [-178.17642, 0.83237, -7.08889]}, "position": {"0.0": [0, -1, -2], "0.25": [0, -1, -4], "0.875": [0, 4, 0], "1.0": [0, 4, -2], "1.0417": [0, 4, -2]}}, "right_arm": {"rotation": {"0.0": [-29.4919, -16.75858, -15.50706], "0.25": [-96.9919, -16.75858, -15.50706], "0.625": [-119.4919, -16.75858, -15.50706], "0.875": [-191.41713, -1.63607, -0.13086], "1.0": [-178.91713, -1.63607, -0.13086], "1.0417": [-178.91713, -1.63607, -0.13086]}, "position": {"0.0": [0, 0, -2], "0.875": [0, 3, -2], "1.0": [0, 3, -3], "1.0417": [0, 3, -3]}}, "sledge_hammer": {"rotation": {"0.0": [13.54545, -60.57268, 9.70542], "0.25": [60.61249, -82.41079, -38.84034], "0.625": [111.8209, -84.16497, -105.40366], "0.875": [141.16486, -84.63897, -137.53417], "1.0417": [141.16486, -84.63897, -137.53417]}, "position": {"0.0": [2, -2, 0], "1.0417": [2, -2, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.5": [0, 0, -67.5], "0.875": [0, 0, -40], "1.0": [0, 0, -7.5], "1.0417": [0, 0, -7.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.5": [0, 0, 67.5], "0.875": [0, 0, 90], "1.0": [0, 0, 0], "1.0417": [0, 0, 0]}}, "left_leg": {"rotation": {"0.0": [0, -22.5, 0], "0.4167": [32.5, -22.5, 0], "0.75": [78.44984, -2.92964, -4.25087], "1.0": [88.2003, -2.7834, 0.75322], "1.0417": [88.2003, -2.7834, 0.75322]}, "position": {"0.0": [1, 0, 0], "0.4167": [1, -2, -5.5], "0.75": [1, -12, -5.5], "1.0": [1, -12.75, -6.5], "1.0417": [1, -12.75, -6.5]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.5": [20.94857, 8.55322, -5.74837], "0.75": [71.02345, 7.27525, -2.44249], "1.0": [86.05549, -0.20689, -1.92385], "1.0417": [86.05549, -0.20689, -1.92385]}, "position": {"0.0": [-1, 0, 0], "0.5": [-1, -1, -5], "0.75": [-1.5, -12, -5.25], "1.0": [-1.5, -13, -6.75], "1.0417": [-1.5, -13, -6.75]}}, "eyes": {"scale": {"0.0": [1, 1, 1], "0.5": [1, 0.1, 1], "1.0417": [1, 0.1, 1]}}, "root": {"rotation": {"0.0": [0, 0, 0], "0.75": [0, 0, 0], "1.0": [0, 0, 0], "1.0417": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.75": [0, 0, 0], "0.9167": [0, 0, 1], "0.9583": [0, -1, 1], "1.0": [0, 0, 1], "1.0417": [0, -1, 1]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.25": [-30, 0, 0], "0.75": [-8.75966, 16.53463, 5.98457], "0.9167": [0, 72.5, 0], "1.0417": [0, 72.5, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [0, 0, 0]}}}}, "animation.ptd_bb.piglin_maruarder.dead": {"loop": true, "bones": {"torso": {"rotation": {"0.0": {"post": [90, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -12, -6], "lerp_mode": "catmullrom"}}}, "left_arm": {"rotation": {"0.0": {"post": [-178.17642, 0.83237, -7.08889], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 4, -2], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-178.91713, -1.63607, -0.13086], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 3, -3], "lerp_mode": "catmullrom"}}}, "sledge_hammer": {"rotation": {"0.0": {"post": [141.16486, -84.63897, -137.53417], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [2, -2, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [0, 0, -7.5], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [88.2003, -2.7834, 0.75322], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, -12.75, -6.5], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [86.05549, -0.20689, -1.92385], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1.5, -13, -6.75], "lerp_mode": "catmullrom"}}}, "eyes": {"scale": {"0.0": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}}}, "root": {"rotation": [0, 0, 0], "position": [0, -1, 1]}, "head": {"rotation": {"0.0": {"post": [0, 72.5, 0], "lerp_mode": "catmullrom"}}}}}}}
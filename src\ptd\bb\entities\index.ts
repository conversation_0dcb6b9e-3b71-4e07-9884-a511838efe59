import { Entity, EntityDamageSource, EffectType, EffectTypes, system, world } from "@minecraft/server";
import { handleLootMechanics } from "./lootMechanics";
import { zombieBruteMechanics } from "../bosses/necromancer/minions/zombie_brute/index";
import { piglinBruteMechanics } from "../bosses/piglin_champion/minions/piglin_brute/index";
import { piglinMarauderMechanics } from "../bosses/piglin_champion/minions/piglin_marauder/index";
import { piglinChampionMechanics } from "../bosses/piglin_champion/index";
import { grimhowlMechanics } from "../bosses/grimhowl/index";
import { necromancerMechanics } from "../bosses/necromancer/index";
import { voidHydraRightHeadMechanics } from "../bosses/void_hydra/heads/right/index";

export const entitiesToBeResetOnLoad = new Set<string>([
  "ptd_bb:necromancer",
  "ptd_bb:piglin_champion",
  "ptd_bb:zombie_brute",
  "ptd_bb:piglin_brute",
  "ptd_bb:piglin_marauder",
  "ptd_bb:void_hydra",
  "ptd_bb:void_hydra_right_head",
  "ptd_bb:void_hydra_middle_head",
  "ptd_bb:void_hydra_left_head",
  "ptd_bb:wardzilla"
]);

// Global entity event listeners
world.afterEvents.entityHurt.subscribe((event: { damage: number; damageSource: EntityDamageSource; hurtEntity: Entity }) => {
  if (event.hurtEntity.typeId === "ptd_bb:grimhowl") {
    grimhowlMechanics(event, "hurt");
  } else if (event.hurtEntity.typeId === "ptd_bb:piglin_champion") {
    piglinChampionMechanics(event.hurtEntity, "hurt");
  }
});

world.afterEvents.entityLoad.subscribe((event: { entity: Entity }) => {
  const entity = event.entity;

  // Handle grimhowl specific load mechanics
  if (entity.typeId === "ptd_bb:grimhowl") {
    grimhowlMechanics(event, "load");
    return;
  }

  // Check if entity needs to be reset on load
  if (entitiesToBeResetOnLoad.has(entity.typeId)) {
    try {
      const isDead = entity.getProperty("ptd_bb:dead") as boolean;
      if (isDead) return; // Don't reset dead entities

      // Trigger the on_load event
      entity.triggerEvent("ptd_bb:on_load");
    } catch (error) {
      console.warn(`Error resetting entity ${entity.typeId} on load: ${error}`);
    }
  }
});

world.afterEvents.dataDrivenEntityTrigger.subscribe(async (event) => {
  const entity = event.entity;
  const entityTypeId = entity.typeId;
  const eventId = event.eventId;

  // Do not wait 1 tick for this particuler event
  if (eventId === "ptd_bb:arcane_blast_explode") {
    // Handle projectile explosion effects
    applyWitherEffectToNearbyEntities(entity);
  }
  // Wait 1 tick so the selected attack is set properly
  await system.waitTicks(1);

  // Handle attack logic
  if (eventId === "ptd_bb:attack") {
    switch (entityTypeId) {
      case "ptd_bb:necromancer":
        necromancerMechanics(entity);
        break;
      case "ptd_bb:zombie_brute":
        zombieBruteMechanics(entity);
        break;
      case "ptd_bb:piglin_champion":
        piglinChampionMechanics(entity);
        break;
      case "ptd_bb:piglin_brute":
        piglinBruteMechanics(entity);
        break;
      case "ptd_bb:piglin_marauder":
        piglinMarauderMechanics(entity);
        break;
      case "ptd_bb:void_hydra_right_head":
        voidHydraRightHeadMechanics(entity);
        break;
      default:
        break;
    }
  }

  // Handle death logic
  if (eventId === "ptd_bb:dead") {
    switch (entityTypeId) {
      case "ptd_bb:necromancer":
        deathMechanics(entity, 150); // 150 ticks = 7.5 seconds death animation + 20 ticks = 170 ticks total
        break;
      case "ptd_bb:piglin_champion":
        deathMechanics(entity, 149);
        break;
      case "ptd_bb:skeleton_soul":
        deathMechanics(entity, 10);
        break;
      case "ptd_bb:winged_zombie":
        deathMechanics(entity, 36);
        break;
      case "ptd_bb:zombie_brute":
        deathMechanics(entity, 35);
        break;
      case "ptd_bb:piglin_brute":
        deathMechanics(entity, 36);
        break;
      case "ptd_bb:piglin_marauder":
        deathMechanics(entity, 36);
        break;
      case "ptd_bb:void_hydra":
        deathMechanics(entity, 496);
        break;
      case "ptd_bb:void_hydra_right_head":
        deathMechanics(entity, 496);
        break;
      case "ptd_bb:void_hydra_mid_head":
        deathMechanics(entity, 496);
        break;
      case "ptd_bb:void_hydra_left_head":
        deathMechanics(entity, 496);
        break;
      default:
        break;
    }
  }
});

function deathMechanics(entity: Entity, ticks: number) {
  if (!entity) return;

  const deathProp = entity.getProperty("ptd_bb:dead") as boolean;

  if (deathProp) {
    // Check if death mechanics are already running for this entity
    const deathMechanicsRunning = entity.getDynamicProperty("ptd_bb:death_mechanics_running") as boolean;
    if (deathMechanicsRunning) {
      // Death mechanics are already running, don't start another instance
      return;
    }

    // Mark that death mechanics are now running
    entity.setDynamicProperty("ptd_bb:death_mechanics_running", true);

    let currentTick = 0;
    let deathInterval: number;

    // Create an interval that runs death mechanics every tick for the specified duration
    deathInterval = system.runInterval(() => {
      try {
        // Check if entity is still valid
        if (!entity) {
          if (deathInterval !== undefined) {
            system.clearRun(deathInterval);
          }
          return;
        }

        // Execute death mechanics for this tick
        handleLootMechanics(entity, currentTick + 1);

        currentTick++;

        // Clear interval when we've reached the specified duration
        if (currentTick >= ticks) {
          if (deathInterval !== undefined) {
            system.clearRun(deathInterval);
          }
          // Clear the running flag when death mechanics complete
          try {
            entity.setDynamicProperty("ptd_bb:death_mechanics_running", false);
          } catch (e) {
            // Entity might be removed, ignore error
          }
        }
      } catch (error) {
        // Clear interval on any error to prevent memory leaks
        if (deathInterval !== undefined) {
          system.clearRun(deathInterval);
        }
        console.warn(`Error in death mechanics interval: ${error}`);
      }
    }, 1); // Run every tick
  }
  return;
}

/**
 * Applies wither effect to entities near the explosion location
 * @param projectile The exploding projectile entity
 */
function applyWitherEffectToNearbyEntities(projectile: Entity): void {
  try {
    if (!projectile) return;

    const explosionRadius = 4;
    const dimension = projectile.dimension;
    const location = projectile.location;

    // Get wither effect
    const witherEffect = EffectTypes.get("minecraft:wither");
    let effectToApply: EffectType | undefined = witherEffect;

    if (!witherEffect) {
      // Fallback to weakness effect if wither is not available
      effectToApply = EffectTypes.get("minecraft:weakness");
    }

    if (!effectToApply) {
      console.warn("No suitable effect found for arcane blast explosion");
      return;
    }
    // Find nearby entities to affect
    const nearbyEntities = dimension.getEntities({
      location: location,
      maxDistance: explosionRadius,
      excludeTypes: ["minecraft:xp_orb", "minecraft:item", "ptd_bb:rock", "ptd_bb:flying_skull"],
      excludeFamilies: ["necromancer", "inanimate"]
    });

    // Apply effect to each nearby entity
    nearbyEntities.forEach((entity) => {
      try {
        entity.addEffect(effectToApply, 200, { amplifier: 1, showParticles: true });
      } catch (effectError) {
        console.warn(`Failed to apply effect to ${entity.typeId}: ${effectError}`);
      }
    });
  } catch (error) {
    console.warn(`Error in applyWitherEffectToNearbyEntities: ${error}`);
    return;
  }
  return;
}

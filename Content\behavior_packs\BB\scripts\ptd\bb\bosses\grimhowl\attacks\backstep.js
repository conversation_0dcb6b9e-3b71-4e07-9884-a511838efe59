/**
 * Handles the backstep move for the Grimhowl entity.
 */
import { system, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { getDirection } from "../../../utilities/vector3";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Handles the backstep move for the Grimhowl entity.
 * @param {Entity} sourceEntity - The entity performing the attack.
 * @param {Entity} targetEntity - The target entity to backstep against.
 */
export function doGrimhowlBackstep(sourceEntity, targetEntity) {
    try {
        if (!sourceEntity)
            return;
        const isSwordMode = sourceEntity.getProperty("ptd_bb:sword_mode");
        sourceEntity.triggerEvent("ptd_bb:grimhowl_backstep");
        const grimHowlLocation = sourceEntity.location;
        const facingLocation = targetEntity.location;
        sourceEntity.teleport(grimHowlLocation, { facingLocation: facingLocation, keepVelocity: true });
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            const viewDirection = sourceEntity.getViewDirection();
            const impulse = { x: viewDirection.x * -2.5, y: 0.5, z: viewDirection.z * -2.5 };
            sourceEntity.applyImpulse(impulse);
            const nearbyEntities = sourceEntity.dimension
                .getEntities({ location: grimHowlLocation, excludeTypes: ["minecraft:item", "minecraft:xp_orb"], maxDistance: 8 })
                .filter(filterValidTargets(sourceEntity));
            nearbyEntities.forEach((entity) => {
                const direction = getDirection(grimHowlLocation, entity.location);
                const magnitude = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
                const normalizedDirection = magnitude === 0 ? { x: 0, y: 0, z: 0 } : { x: direction.x / magnitude, y: direction.y / magnitude, z: direction.z / magnitude };
                const isSneaking = entity.isSneaking;
                if (isSwordMode) {
                    sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + Math.random() * 0.3}`);
                    if (isSneaking) {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 1.25, 0.25);
                    }
                    else {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 2.5, 0.5);
                    }
                    entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.backstep_sword.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                }
                else {
                    if (isSneaking) {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 0.5, 0.25);
                    }
                    else {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 1, 0.5);
                    }
                }
            });
        }, 20 * 0.38);
    }
    catch (error) {
        sourceEntity?.triggerEvent("ptd_bb:attack_done");
    }
}

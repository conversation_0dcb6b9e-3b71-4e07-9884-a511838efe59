import { <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { getDistance } from "../../../../../utilities/vector3";

/**
 * Attack timing in ticks - when the damage should be applied during the animation
 * Based on the sweep animation, damage should occur around 50% through (around tick 15)
 */
const ATTACK_TIMING = 15;

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 28; // 1.375 seconds

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20; // 1 second

/**
 * Normalizes a 2D direction vector
 * @param x X component
 * @param z Z component
 * @returns Normalized direction vector
 */
function normalizeDirection(x: number, z: number): { x: number; z: number } {
  const length = Math.sqrt(x * x + z * z);
  if (length > 0) {
    return { x: x / length, z: z / length };
  }
  return { x: 0, z: 0 };
}

/**
 * Executes the sweep attack for the Piglin Marauder
 * Creates a wide sweeping attack that damages entities in an arc in front of the marauder
 *
 * @param piglinMarauder The piglin marauder entity
 */
export function executeSweepAttack(piglinMarauder: Entity): void {
  // Attack parameters
  const damageRadius = 5; // Wide sweep range
  const damage = 8; // Lower damage than slam but wider area

  // Wait for the attack timing before executing the attack
  let timing = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }

      // Get the marauder's current target for direction calculation
      const target = getTarget(piglinMarauder, piglinMarauder.location, 64, ["piglin_champion", "piglin", "rock"]);
      let dirX = 0;
      let dirZ = 1; // Default forward direction

      if (target) {
        const dx = target.location.x - piglinMarauder.location.x;
        const dz = target.location.z - piglinMarauder.location.z;
        const normalized = normalizeDirection(dx, dz);
        dirX = normalized.x;
        dirZ = normalized.z;
      }

      // Calculate position 1.5 block in front of the marauder
      const originPos: Vector3 = {
        x: piglinMarauder.location.x + dirX * 1.5,
        y: piglinMarauder.location.y + 1.75,
        z: piglinMarauder.location.z + dirZ * 1.5
      };

      // Play attack impact sound and particles
      piglinMarauder.dimension.spawnParticle("minecraft:critical_hit_emitter", originPos);
      piglinMarauder.dimension.playSound("mob.piglin.angry", originPos, { volume: 1.8, pitch: 0.5 });
      piglinMarauder.dimension.playSound("random.break", originPos, { volume: 1.2, pitch: 0.7 });

      // Find entities within the damage radius
      const entities = piglinMarauder.dimension.getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
      });

      entities.forEach((entity: Entity) => {
        // Calculate angle between marauder's facing direction and entity direction
        const entityDx = entity.location.x - piglinMarauder.location.x;
        const entityDz = entity.location.z - piglinMarauder.location.z;
        const entityNormalized = normalizeDirection(entityDx, entityDz);

        // Calculate dot product to determine if entity is in front arc (120 degrees)
        const dotProduct = dirX * entityNormalized.x + dirZ * entityNormalized.z;
        const angleThreshold = Math.cos(Math.PI / 3); // 60 degrees on each side = 120 degree arc

        // Only damage entities in the front arc
        if (dotProduct >= angleThreshold) {
          // Calculate distance for knockback
          const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
          const point2: Vector3 = { x: piglinMarauder.location.x, y: 0, z: piglinMarauder.location.z };
          const distance = getDistance(point1, point2);

          if (distance > 0) {
            // Use entity direction for knockback (away from marauder)
            const nx = entityNormalized.x;
            const nz = entityNormalized.z;

            // Sweep attack parameters - moderate knockback
            const horizontalStrength = 2.5;
            const verticalStrength = 0.3;

            try {
              // Try to apply knockback first
              if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.Survival || gameMode === GameMode.Adventure) {
                  entity.applyKnockback({ x: nx * horizontalStrength, z: nz * horizontalStrength }, verticalStrength);
                }
              } else {
                entity.applyKnockback({ x: nx * horizontalStrength, z: nz * horizontalStrength }, verticalStrength);
              }
            } catch (e) {
              // Fallback to applyImpulse if applyKnockback fails
              const impulse: Vector3 = { x: nx * horizontalStrength, y: verticalStrength, z: nz * horizontalStrength };

              entity.applyImpulse(impulse);
            }
          }

          // Apply damage after knockback/impulse
          entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinMarauder });
        }
      });

      // Clear the timeout
      system.clearRun(timing);
    } catch (error) {
      // handle error silently
      system.clearRun(timing);
    }
  }, ATTACK_TIMING);

  // Reset the attack state to "none" after the animation is complete
  let reset = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(reset);
        return;
      }
      piglinMarauder.triggerEvent("ptd_bb:reset_attack");
      system.clearRun(reset);
    } catch (error) {
      // handle error silently
      system.clearRun(reset);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to select the next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldown);
        return;
      }
      piglinMarauder.setProperty("ptd_bb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      // handle error silently
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);

  return;
}

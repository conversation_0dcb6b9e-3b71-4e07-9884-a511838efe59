/**
 * Piglin Champion attack damages as direct values
 */
export const PIGLIN_CHAMPION_ATTACK_DAMAGES = {
    horizontal: { damage: 16 },
    vertical: { axe: { damage: 16 }, rocks: { damage: 8 } },
    foot_stomp: { damage: 12 },
    spin_slam: { damage: 12 },
    body_slam: { damage: 18 },
    charging: { damage: 7 }
};
/**
 * Necromancer attack damages as direct values
 */
export const NECROMANCER_ATTACK_DAMAGES = { cataclysm: { damage: 6 }, soul_drain: { damage: 7 } };
/**
 * Grimhowl attack damages as direct values
 */
export const GRIMHOWL_ATTACK_DAMAGES = {
    left_claw: { damage: 4 },
    right_claw: { damage: 4 },
    backstep_sword: { damage: 5 },
    pounce: { damage: 8 },
    shadow_onslaught: { damage: 8 },
    slash: { damage: 8 },
    spinning_slash: { damage: 6 },
    roar: { damage: 2 },
    collision_damage: { damage: 4 }
};
/**
 * Void Hydra attack damages as direct values
 * Balanced for a high-tier boss with 1500 health
 */
export const VOID_HYDRA_ATTACK_DAMAGES = {
    right_atomic_cross: { damage: 8 },
    right_atomic: { damage: 8 },
    right_vacuum: { damage: 8 }, // Continuous damage over time
    mid_atomic: { damage: 12 },
    mid_meteor: { damage: 18 },
    mid_singularity: { damage: 20 },
    left_atomic_cross: { damage: 14 },
    left_atomic: { damage: 12 },
    left_railgun: { damage: 22 }, // High damage, precise attack
    left_missile: { damage: 15 },
    left_shout: { damage: 10 } // Area effect with knockback
};

import { EntityDamageCause, system } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Performs spinning slash attack for the Grimhowl boss
 * The attack creates a sweeping motion in front of the boss
 * and damages entities within the attack radius
 * @param sourceEntity - The Grimhowl boss entity
 * @param nearbyTargets - Array of entities within the attack radius
 */
export function doGrimhowlSpinningSlash(sourceEntity, nearbyTargets) {
    try {
        const isEnraged = sourceEntity.getProperty("ptd_bb:enraged");
        const sourceLocation = sourceEntity.location;
        if (isEnraged) {
            sourceEntity.triggerEvent("ptd_bb:grimhowl_spinning_slash_enraged");
        }
        else {
            sourceEntity.triggerEvent("ptd_bb:grimhowl_spinning_slash");
        }
        if (nearbyTargets.length === 0) {
            sourceEntity.triggerEvent("ptd_bb:attack_done");
            return;
        }
        sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 5${1 + Math.random() * 0.3}`);
        // First sweep: front (0 deg) to back (180 deg), starts at 0.75s, lasts 0.5s
        system.runTimeout(() => {
            sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + Math.random() * 0.3}`);
            // Sweep from front (0) to back (-180)
            sweepAttack(sourceEntity, sourceLocation, 0, -180, 8, GRIMHOWL_ATTACK_DAMAGES.spinning_slash.damage);
        }, 0.75 * 20); // 0.75s
        // Second sweep: back (180 deg) to front (0 deg), starts at 1.5s, lasts 0.5s
        system.runTimeout(() => {
            sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + Math.random() * 0.3}`);
            // Sweep from front-right (+90) to front-left (+270)
            sweepAttack(sourceEntity, sourceLocation, -180, -360, 8, GRIMHOWL_ATTACK_DAMAGES.spinning_slash.damage);
        }, 1.33 * 20); // 1.5s
    }
    catch (error) {
        sourceEntity?.triggerEvent("ptd_bb:attack_done");
    }
}
/**
 * Executes a sweeping attack for the Grimhowl boss
 * The attack creates a cone of damage in front of the boss
 *
 * @param sourceEntity - The Grimhowl boss entity
 * @param sourceLocation - The location of the Grimhowl boss
 * @param startAngle - The starting angle of the sweep in degrees
 * @param endAngle - The ending angle of the sweep in degrees
 * @param steps - The number of steps in the sweep
 * @param damage - The damage dealt by the attack
 */
function sweepAttack(sourceEntity, sourceLocation, startAngle, endAngle, steps, damage) {
    const radius = 5;
    const yOffset = 1.1; // Adjust Y offset to match entity height
    const angleStep = (endAngle - startAngle) / steps;
    const isSwordMode = sourceEntity.getProperty("ptd_bb:sword_mode");
    for (let i = 0; i <= steps; i++) {
        system.runTimeout(() => {
            const angle = startAngle + angleStep * i;
            const rad = angle * (Math.PI / 180);
            const viewDir = sourceEntity.getViewDirection();
            // Calculate base yaw from view direction
            const baseYaw = Math.atan2(viewDir.x, viewDir.z);
            const sweepYaw = baseYaw + rad;
            const hitboxLocation = {
                x: sourceLocation.x + Math.sin(sweepYaw) * radius,
                y: sourceLocation.y + yOffset,
                z: sourceLocation.z + Math.cos(sweepYaw) * radius
            };
            const targets = sourceEntity.dimension
                .getEntities({ location: hitboxLocation, excludeTypes: ["minecraft:item", "minecraft:xp_orb"], maxDistance: isSwordMode ? 7 : 4 })
                .filter(filterValidTargets(sourceEntity));
            targets.forEach((entity) => {
                entity.applyDamage(damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
            if (i % 5 === 0) {
                if (isSwordMode) {
                    if (i % 10 === 0)
                        sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + Math.random() * 0.3}`);
                }
                else {
                    sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + Math.random() * 0.3}`);
                    sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + Math.random() * 0.3}`);
                }
            }
        }, Math.round(i * (10 / steps))); // 0.5s sweep duration in ticks
    }
}

{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_bb:rock", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:despawn": {"minecraft:instant_despawn": {}}}, "events": {"ptd_bb:despawn": {"add": {"component_groups": ["minecraft:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0.7, "height": 0.7}, "minecraft:is_collidable": {}, "minecraft:type_family": {"family": ["inanimate", "rock"]}, "minecraft:timer": {"time": [4, 7], "looping": false, "time_down_event": {"event": "ptd_bb:despawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:body_rotation_blocked": {}}}}
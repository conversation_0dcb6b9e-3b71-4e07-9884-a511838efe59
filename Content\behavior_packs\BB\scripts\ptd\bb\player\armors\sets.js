import { EquipmentSlot, EntityComponentTypes, GameMode } from "@minecraft/server";
import { handlePiglinChampionArmor } from "./piglinChampion";
/**
 * Available armor sets configuration
 */
export const ARMOR_SETS = [
    {
        id: "piglin_champion",
        name: "Piglin Champion",
        head: "ptd_bb:piglin_champion_helmet",
        chest: "ptd_bb:piglin_champion_chestplate",
        legs: "ptd_bb:piglin_champion_leggings",
        feet: "ptd_bb:piglin_champion_boots"
    }
];
/**
 * Detects if a player is wearing a complete armor set
 * @param player The player to check
 * @returns ArmorSetDetection result
 */
export function detectArmorSet(player) {
    // Skip spectator players
    if (player.getGameMode() === GameMode.Spectator) {
        return { isWearing: false };
    }
    const equipmentComponent = player.getComponent(EntityComponentTypes.Equippable);
    if (!equipmentComponent) {
        return { isWearing: false };
    }
    // Get equipped armor pieces
    const headItem = equipmentComponent.getEquipment(EquipmentSlot.Head);
    const chestItem = equipmentComponent.getEquipment(EquipmentSlot.Chest);
    const legsItem = equipmentComponent.getEquipment(EquipmentSlot.Legs);
    const feetItem = equipmentComponent.getEquipment(EquipmentSlot.Feet);
    // Check each armor set
    for (const armorSet of ARMOR_SETS) {
        const isComplete = headItem?.typeId === armorSet.head &&
            chestItem?.typeId === armorSet.chest &&
            legsItem?.typeId === armorSet.legs &&
            feetItem?.typeId === armorSet.feet;
        if (isComplete) {
            return { isWearing: true, armorSet };
        }
    }
    return { isWearing: false };
}
/**
 * Main handler for armor set mechanics - called every tick for each player
 * @param player The player to handle armor sets for
 */
export function handleArmorSets(player) {
    const detection = detectArmorSet(player);
    if (!detection.isWearing || !detection.armorSet) {
        return;
    }
    // Handle specific armor set mechanics
    switch (detection.armorSet.id) {
        case "piglin_champion":
            handlePiglinChampionArmor(player);
            break;
        // Add more armor sets here as needed
    }
}
